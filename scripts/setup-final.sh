#!/bin/bash

# Final Setup and Deployment Script
# This script helps complete the bidirectional sync setup

set -e

PRIVATE_DOCS_PATH="/Users/<USER>/code/github.com/danielfbm/private-docs"
DOCS_SYNC_PATH="/Users/<USER>/code/github.com/danielfbm/docs-sync"

echo "🚀 Final Setup for Bidirectional Documentation Sync"
echo "=================================================="

# Function to show git status for both repos
show_git_status() {
    echo "📊 Git Status Check"
    echo "=================="

    echo "📁 private-docs repository:"
    cd "$PRIVATE_DOCS_PATH"
    echo "Current branch: $(git branch --show-current)"
    if git status --porcelain | grep -q .; then
        echo "📝 Uncommitted changes found:"
        git status --short
    else
        echo "✅ Working directory clean"
    fi

    echo ""
    echo "📁 docs-sync repository:"
    cd "$DOCS_SYNC_PATH"
    echo "Current branch: $(git branch --show-current)"
    if git status --porcelain | grep -q .; then
        echo "📝 Uncommitted changes found:"
        git status --short
    else
        echo "✅ Working directory clean"
    fi
    echo ""
}

# Function to commit workflow files
commit_workflow_files() {
    echo "💾 Committing Workflow Files"
    echo "==========================="

    # Commit to private-docs
    cd "$PRIVATE_DOCS_PATH"
    if git status --porcelain | grep -q .; then
        echo "📝 Committing changes to private-docs..."
        git add .
        git commit -m "Add bidirectional documentation sync workflows

Features:
- Forward sync from private-docs to docs-sync
- Reverse sync from docs-sync to private-docs via PR
- Loop prevention mechanisms
- Comprehensive documentation and testing scripts

Files added:
- .github/workflows/sync-docs-advanced.yml (forward sync)
- .github/BIDIRECTIONAL_SYNC.md (documentation)
- scripts/test-bidirectional-sync.sh (testing)

This enables automatic sync of documentation changes while preventing
infinite loops and supporting external contributions."
        echo "✅ Changes committed to private-docs"
    else
        echo "ℹ️ No changes to commit in private-docs"
    fi

    # Commit to docs-sync
    cd "$DOCS_SYNC_PATH"
    if git status --porcelain | grep -q .; then
        echo "📝 Committing changes to docs-sync..."
        git add .
        git commit -m "Add reverse sync workflow for external contributions

This workflow enables external contributors' changes to be synced back
to the private-docs repository via pull requests.

Features:
- Triggers on merged PRs with documentation changes
- Creates PRs in private-docs for manual review
- Prevents sync loops by detecting bot-generated PRs
- Comprehensive logging and error handling

File added:
- .github/workflows/reverse-sync.yml"
        echo "✅ Changes committed to docs-sync"
    else
        echo "ℹ️ No changes to commit in docs-sync"
    fi
}

# Function to show next steps
show_next_steps() {
    echo "📋 Next Steps"
    echo "============"
    echo ""
    echo "1. 🔐 Set up GitHub Personal Access Token (PAT):"
    echo "   - Go to GitHub Settings → Developer settings → Personal access tokens"
    echo "   - Create a token with 'repo' and 'workflow' scopes"
    echo "   - Save the token securely"
    echo ""
    echo "2. 🔑 Add SYNC_TOKEN secret to both repositories:"
    echo "   - private-docs: https://github.com/danielfbm/private-docs/settings/secrets/actions"
    echo "   - docs-sync: https://github.com/danielfbm/docs-sync/settings/secrets/actions"
    echo "   - Secret name: SYNC_TOKEN"
    echo "   - Secret value: Your PAT from step 1"
    echo ""
    echo "3. 🚀 Push the workflow files:"
    echo "   - cd $PRIVATE_DOCS_PATH && git push origin main"
    echo "   - cd $DOCS_SYNC_PATH && git push origin main"
    echo ""
    echo "4. 🧪 Test the sync:"
    echo "   - Edit a file in private-docs/docs/ and push to main"
    echo "   - Check that it syncs to docs-sync automatically"
    echo "   - Create a test PR from external contributor in docs-sync"
    echo "   - Verify it creates a PR in private-docs"
    echo ""
    echo "5. 📚 Documentation locations:"
    echo "   - Setup guide: $PRIVATE_DOCS_PATH/.github/SYNC_SETUP.md"
    echo "   - Bidirectional sync: $PRIVATE_DOCS_PATH/.github/BIDIRECTIONAL_SYNC.md"
    echo ""
}

# Function to show workflow summary
show_workflow_summary() {
    echo "⚡ Workflow Summary"
    echo "=================="
    echo ""
    echo "🔄 Forward Sync (private-docs → docs-sync):"
    echo "   - Trigger: Push to main with docs changes"
    echo "   - Action: Auto-sync to docs-sync"
    echo "   - Loop prevention: Skips [reverse-sync] commits"
    echo ""
    echo "🔄 Reverse Sync (docs-sync → private-docs):"
    echo "   - Trigger: Merged PR with docs changes"
    echo "   - Action: Creates PR in private-docs"
    echo "   - Loop prevention: Skips bot PRs"
    echo ""
    echo "🛡️ Safety Features:"
    echo "   - Automatic loop detection and prevention"
    echo "   - Manual review for external contributions"
    echo "   - Comprehensive logging and error handling"
    echo "   - Backup creation before sync"
    echo ""
}

# Main execution
main() {
    show_git_status

    echo "❓ Do you want to commit the workflow files now? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        commit_workflow_files
        echo ""
        show_git_status
    fi

    echo ""
    show_workflow_summary
    echo ""
    show_next_steps

    echo ""
    echo "🎉 Bidirectional sync setup is complete!"
    echo "💡 Remember to set up the SYNC_TOKEN secret before the workflows will work."
}

# Run main function
main

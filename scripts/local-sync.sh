#!/bin/bash

# Local Documentation Sync Script
# This script simulates what the GitHub Actions workflow does locally

set -e

# Configuration
PRIVATE_DOCS_PATH="/Users/<USER>/code/github.com/danielfbm/private-docs"
DOCS_SYNC_PATH="/Users/<USER>/code/github.com/danielfbm/docs-sync"

echo "🚀 Starting local documentation sync..."
echo "Source: $PRIVATE_DOCS_PATH"
echo "Target: $DOCS_SYNC_PATH"

# Check if directories exist
if [ ! -d "$PRIVATE_DOCS_PATH" ]; then
    echo "❌ Error: private-docs directory not found at $PRIVATE_DOCS_PATH"
    exit 1
fi

if [ ! -d "$DOCS_SYNC_PATH" ]; then
    echo "❌ Error: docs-sync directory not found at $DOCS_SYNC_PATH"
    exit 1
fi

# Navigate to docs-sync directory
cd "$DOCS_SYNC_PATH"

# Check if it's a git repository
if [ ! -d ".git" ]; then
    echo "❌ Error: docs-sync is not a git repository"
    exit 1
fi

# Create backup
echo "📁 Creating backup..."
mkdir -p backup
if [ -d "docs" ]; then
    cp -r docs backup/docs-backup-$(date +%Y%m%d-%H%M%S)
fi
if [ -f "README.md" ]; then
    cp README.md backup/README-backup-$(date +%Y%m%d-%H%M%S).md
fi

# Remove existing files
echo "🧹 Removing existing documentation..."
rm -rf docs README.md

# Copy new files
echo "📋 Copying documentation from private-docs..."
if [ -d "$PRIVATE_DOCS_PATH/docs" ]; then
    cp -r "$PRIVATE_DOCS_PATH/docs" .
    echo "✅ Copied docs directory"
else
    echo "⚠️  No docs directory found in private-docs"
fi

if [ -f "$PRIVATE_DOCS_PATH/README.md" ]; then
    cp "$PRIVATE_DOCS_PATH/README.md" .
    echo "✅ Copied README.md"
else
    echo "⚠️  No README.md found in private-docs"
fi

# Create sync info
echo "📝 Creating sync information..."
cat > SYNC_INFO.md << EOF
# Documentation Sync Information

- **Last synced**: $(date -u '+%Y-%m-%d %H:%M:%S UTC')
- **Source repository**: danielfbm/private-docs (local)
- **Synced by**: $(whoami)
- **Sync method**: Local script

## Files synced:
- docs/ directory
- README.md

EOF

if [ -d "docs" ]; then
    file_count=$(find docs -type f | wc -l | tr -d ' ')
    echo "- Total files: $file_count" >> SYNC_INFO.md
fi

# Check git status
echo "📊 Checking for changes..."
git add .

if git diff --staged --quiet; then
    echo "ℹ️  No changes detected"
else
    echo "📦 Changes detected, creating commit..."

    # Get source commit info if available
    source_commit=""
    if [ -d "$PRIVATE_DOCS_PATH/.git" ]; then
        cd "$PRIVATE_DOCS_PATH"
        source_commit=$(git log -1 --pretty=format:"%h - %s (%an)")
        cd "$DOCS_SYNC_PATH"
    fi

    # Create commit message
    commit_msg="📚 Local sync documentation from private-docs

Synced on $(date -u '+%Y-%m-%d %H:%M:%S UTC')
Source commit: $source_commit
Synced by: $(whoami)"

    git commit -m "$commit_msg"
    echo "✅ Commit created successfully"

    echo ""
    echo "🎯 Next steps:"
    echo "1. Review the changes: git log -1"
    echo "2. Push to remote: git push origin main"
    echo "3. Or undo changes: git reset HEAD~1"
fi

echo ""
echo "🎉 Local sync completed!"
echo "💡 Run 'git status' to see the current state"

#!/bin/bash

# Bidirectional Sync Test Script
# This script helps test the bidirectional sync setup

set -e

# Configuration
PRIVATE_DOCS_PATH="/Users/<USER>/code/github.com/danielfbm/private-docs"
DOCS_SYNC_PATH="/Users/<USER>/code/github.com/danielfbm/docs-sync"

echo "🧪 Bidirectional Sync Test Script"
echo "=================================="

# Check if directories exist
if [ ! -d "$PRIVATE_DOCS_PATH" ]; then
    echo "❌ Error: private-docs directory not found at $PRIVATE_DOCS_PATH"
    exit 1
fi

if [ ! -d "$DOCS_SYNC_PATH" ]; then
    echo "❌ Error: docs-sync directory not found at $DOCS_SYNC_PATH"
    exit 1
fi

# Function to check workflow files
check_workflow_files() {
    echo "📋 Checking workflow files..."

    # Check private-docs workflows
    if [ -f "$PRIVATE_DOCS_PATH/.github/workflows/sync-docs-advanced.yml" ]; then
        echo "✅ Forward sync workflow found in private-docs"
    else
        echo "❌ Forward sync workflow missing in private-docs"
        return 1
    fi

    # Check docs-sync workflows
    if [ -f "$DOCS_SYNC_PATH/.github/workflows/reverse-sync.yml" ]; then
        echo "✅ Reverse sync workflow found in docs-sync"
    else
        echo "❌ Reverse sync workflow missing in docs-sync"
        return 1
    fi

    echo "✅ All workflow files present"
}

# Function to test loop prevention markers
test_loop_prevention() {
    echo "🔄 Testing loop prevention logic..."

    # Test reverse sync marker detection
    test_commit_msg="[reverse-sync] Test commit from docs-sync"
    if echo "$test_commit_msg" | grep -q "\[reverse-sync\]"; then
        echo "✅ Reverse sync marker detection works"
    else
        echo "❌ Reverse sync marker detection failed"
        return 1
    fi

    # Test sync bot detection
    test_pr_author="github-actions[bot]"
    test_pr_title="Sync documentation from private-docs"

    if [[ "$test_pr_author" == "github-actions[bot]" ]] || [[ "$test_pr_title" == *"Sync documentation"* ]]; then
        echo "✅ Sync bot detection works"
    else
        echo "❌ Sync bot detection failed"
        return 1
    fi

    echo "✅ Loop prevention logic working"
}

# Function to verify repository structure
check_repo_structure() {
    echo "📁 Checking repository structure..."

    # Check private-docs structure
    if [ -d "$PRIVATE_DOCS_PATH/docs" ]; then
        echo "✅ docs/ directory found in private-docs"
    else
        echo "⚠️  docs/ directory missing in private-docs"
    fi

    if [ -f "$PRIVATE_DOCS_PATH/README.md" ]; then
        echo "✅ README.md found in private-docs"
    else
        echo "⚠️  README.md missing in private-docs"
    fi

    # Check docs-sync structure
    if [ -d "$DOCS_SYNC_PATH/docs" ]; then
        echo "✅ docs/ directory found in docs-sync"
    else
        echo "⚠️  docs/ directory missing in docs-sync"
    fi

    if [ -f "$DOCS_SYNC_PATH/README.md" ]; then
        echo "✅ README.md found in docs-sync"
    else
        echo "⚠️  README.md missing in docs-sync"
    fi

    echo "✅ Repository structure check complete"
}

# Function to simulate forward sync
simulate_forward_sync() {
    echo "⏭️  Simulating forward sync..."

    # Create a temporary test file
    test_file="$PRIVATE_DOCS_PATH/docs/test-sync.md"
    echo "# Test Sync File" > "$test_file"
    echo "This is a test file for sync verification." >> "$test_file"
    echo "Created on: $(date)" >> "$test_file"

    # Check if the sync would work
    if [ -f "$test_file" ]; then
        echo "✅ Test file created in private-docs"

        # Simulate copy operation
        target_file="$DOCS_SYNC_PATH/docs/test-sync.md"
        cp "$test_file" "$target_file"

        if [ -f "$target_file" ]; then
            echo "✅ Test file successfully copied to docs-sync"

            # Cleanup
            rm "$test_file" "$target_file"
            echo "🧹 Test files cleaned up"
        else
            echo "❌ Failed to copy test file to docs-sync"
            rm "$test_file"
            return 1
        fi
    else
        echo "❌ Failed to create test file"
        return 1
    fi

    echo "✅ Forward sync simulation successful"
}

# Function to check git configuration
check_git_config() {
    echo "⚙️  Checking git configuration..."

    cd "$PRIVATE_DOCS_PATH"
    if git status > /dev/null 2>&1; then
        echo "✅ private-docs is a valid git repository"
    else
        echo "❌ private-docs is not a valid git repository"
        return 1
    fi

    cd "$DOCS_SYNC_PATH"
    if git status > /dev/null 2>&1; then
        echo "✅ docs-sync is a valid git repository"
    else
        echo "❌ docs-sync is not a valid git repository"
        return 1
    fi

    echo "✅ Git configuration check complete"
}

# Function to check for required tools
check_dependencies() {
    echo "🔧 Checking dependencies..."

    # Check for curl (used in reverse sync)
    if command -v curl > /dev/null 2>&1; then
        echo "✅ curl is available"
    else
        echo "❌ curl is not available (needed for GitHub API calls)"
        return 1
    fi

    # Check for git
    if command -v git > /dev/null 2>&1; then
        echo "✅ git is available"
    else
        echo "❌ git is not available"
        return 1
    fi

    echo "✅ All dependencies available"
}

# Function to provide setup verification
verify_setup() {
    echo "🔍 Setup Verification Summary"
    echo "============================"

    # List what needs to be done manually
    echo "📝 Manual Setup Required:"
    echo "1. Create SYNC_TOKEN secret in both repositories"
    echo "2. Ensure token has 'repo' and 'workflow' scopes"
    echo "3. Test with actual commits and PRs"
    echo ""

    echo "📋 Workflow Locations:"
    echo "- Forward sync: private-docs/.github/workflows/sync-docs-advanced.yml"
    echo "- Reverse sync: docs-sync/.github/workflows/reverse-sync.yml"
    echo ""

    echo "📚 Documentation:"
    echo "- Setup guide: private-docs/.github/SYNC_SETUP.md"
    echo "- Bidirectional sync: private-docs/.github/BIDIRECTIONAL_SYNC.md"
    echo ""

    echo "🧪 Next Steps:"
    echo "1. Commit and push workflow files to respective repositories"
    echo "2. Set up SYNC_TOKEN secret in both repositories"
    echo "3. Test forward sync by modifying docs in private-docs"
    echo "4. Test reverse sync by creating external PR in docs-sync"
}

# Main execution
main() {
    echo "Starting bidirectional sync test..."
    echo ""

    # Run all checks
    check_dependencies
    echo ""

    check_git_config
    echo ""

    check_workflow_files
    echo ""

    check_repo_structure
    echo ""

    test_loop_prevention
    echo ""

    simulate_forward_sync
    echo ""

    verify_setup
    echo ""

    echo "🎉 Test completed successfully!"
    echo "💡 Remember to set up the SYNC_TOKEN secret before using the workflows."
}

# Run the main function
main

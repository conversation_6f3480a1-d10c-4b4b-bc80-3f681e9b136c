# Documentation Sync Scripts

This directory contains helper scripts for managing the bidirectional documentation sync between `private-docs` and `docs-sync` repositories.

## Scripts Overview

### `local-sync.sh`
**Purpose**: Test forward sync locally before using GitHub Actions
- Simulates the forward sync process (private-docs → docs-sync)
- Creates backups before sync
- Provides detailed logging
- Safe to run multiple times

**Usage**:
```bash
./scripts/local-sync.sh
```

### `test-bidirectional-sync.sh`
**Purpose**: Comprehensive test of the bidirectional sync setup
- Validates workflow files exist
- Tests loop prevention logic
- Checks repository structure
- Verifies dependencies
- Simulates sync operations

**Usage**:
```bash
./scripts/test-bidirectional-sync.sh
```

### `setup-final.sh`
**Purpose**: Final setup and deployment helper
- Shows git status for both repositories
- Helps commit workflow files
- Provides next steps for completion
- Summarizes the sync system

**Usage**:
```bash
./scripts/setup-final.sh
```

## Workflow Files Created

### Forward Sync (`private-docs/.github/workflows/sync-docs-advanced.yml`)
- Triggers on pushes to main with documentation changes
- Syncs docs/ and README.md to docs-sync repository
- Includes loop prevention for reverse sync commits
- Creates detailed sync information

### Reverse Sync (`docs-sync/.github/workflows/reverse-sync.yml`)
- Triggers on merged PRs with documentation changes
- Creates PRs in private-docs for external contributions
- Prevents loops by detecting sync bot PRs
- Applies only the specific changes from the merged PR

## Setup Process

1. **Run Tests**: `./scripts/test-bidirectional-sync.sh`
2. **Final Setup**: `./scripts/setup-final.sh`
3. **Set up GitHub secrets** (SYNC_TOKEN)
4. **Push workflow files** to both repositories
5. **Test end-to-end** with actual changes

## Security & Loop Prevention

### Forward Sync Loop Prevention
```bash
# Checks commit message for reverse sync marker
if echo "$commit_msg" | grep -q "\[reverse-sync\]"; then
  skip_sync=true
fi
```

### Reverse Sync Loop Prevention
```bash
# Checks PR author and title for sync indicators
if [[ "$pr_author" == "github-actions[bot]" ]] ||
   [[ "$pr_title" == *"Sync documentation"* ]]; then
  skip_sync=true
fi
```

## Troubleshooting

### Common Issues
1. **Permission denied**: Check SYNC_TOKEN has repo and workflow scopes
2. **Workflow not triggering**: Verify file paths in trigger conditions
3. **Infinite loops**: Check commit messages and PR titles for proper markers
4. **Merge conflicts**: Manual resolution required in PRs

### Debug Commands
```bash
# Check workflow status
git status

# View recent commits
git log --oneline -10

# Check for sync markers
git log --grep="\[reverse-sync\]" --oneline

# Test local sync
./scripts/local-sync.sh
```

## Documentation

- **Setup Guide**: `.github/SYNC_SETUP.md`
- **Bidirectional Sync**: `.github/BIDIRECTIONAL_SYNC.md`
- **This README**: `scripts/README.md`

## Flow Diagram

```
External Contributor
        ↓
    docs-sync PR
        ↓
   [Merged] ──→ Reverse Sync Workflow
        ↓
  private-docs PR ──→ [Manual Review]
        ↓
   [Merged] ──→ Forward Sync (SKIPPED due to [reverse-sync] marker)
```

This setup ensures that:
- Internal changes flow automatically to the public repository
- External contributions require manual review
- No infinite sync loops occur
- All changes are properly tracked and attributed

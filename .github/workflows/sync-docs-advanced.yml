name: Advanced Sync Documentation to docs-sync

on:
  push:
    branches:
      - main
      - release-*
    paths:
      - 'docs/**'
      - 'README.md'
  workflow_dispatch:  # Allow manual triggering
    inputs:
      force_sync:
        description: 'Force sync even if no changes detected'
        required: false
        default: 'false'
        type: boolean

jobs:
  sync-docs:
    runs-on: ubuntu-latest

    steps:
    - name: Check if this is a reverse sync commit
      id: check_reverse_sync
      run: |
        commit_msg="${{ github.event.head_commit.message }}"
        if echo "$commit_msg" | grep -q "\[reverse-sync\]"; then
          echo "reverse_sync=true" >> $GITHUB_OUTPUT
          echo "🔄 This commit is from reverse sync - skipping forward sync to prevent loop"
        else
          echo "reverse_sync=false" >> $GITHUB_OUTPUT
          echo "✅ This is a regular commit - proceeding with forward sync"
        fi

    - name: Checkout private-docs repository
      if: steps.check_reverse_sync.outputs.reverse_sync == 'false'
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.SYNC_TOKEN }}
        path: private-docs
        fetch-depth: 0  # Fetch full history for better commit messages
        ref: ${{ github.ref }}

    - name: Checkout docs-sync repository
      if: steps.check_reverse_sync.outputs.reverse_sync == 'false'
      uses: actions/checkout@v4
      with:
        repository: danielfbm/docs-sync
        token: ${{ secrets.SYNC_TOKEN }}
        path: docs-sync
        fetch-depth: 0

    - name: Checkout docs-sync branch
      if: steps.check_reverse_sync.outputs.reverse_sync == 'false'
      run: |
        cd docs-sync
        base_ref=${{ github.event.ref }}
        # Remove refs/heads/ prefix to get just the branch name
        base_ref=${base_ref#refs/heads/}
        echo "base_ref=$base_ref" >> $GITHUB_OUTPUT
        has_remote_branch=$(git branch -r --list "origin/$base_ref")
        if [ -z "$has_remote_branch" ]; then
          git checkout -b $base_ref
        else
          git checkout $base_ref
        fi

    - name: Set up sync environment
      if: steps.check_reverse_sync.outputs.reverse_sync == 'false'
      run: |
        # Create backup of existing docs-sync content
        mkdir -p backup
        if [ -d "docs-sync/docs" ]; then
          cp -r docs-sync/docs backup/docs-backup
        fi
        if [ -f "docs-sync/README.md" ]; then
          cp docs-sync/README.md backup/README-backup.md
        fi

        echo "Backup created successfully"

    - name: Sync documentation files
      if: steps.check_reverse_sync.outputs.reverse_sync == 'false'
      run: |
        set -e  # Exit on any error

        # Remove existing docs in target repo
        rm -rf docs-sync/docs docs-sync/README.md

        # Copy docs folder and README from private-docs to docs-sync
        if [ -d "private-docs/docs" ]; then
          cp -r private-docs/docs docs-sync/
          echo "✓ Copied docs directory"
        else
          echo "⚠️ No docs directory found in private-docs"
        fi

        if [ -f "private-docs/README.md" ]; then
          cp private-docs/README.md docs-sync/
          echo "✓ Copied README.md"
        else
          echo "⚠️ No README.md found in private-docs"
        fi

        # Create sync metadata
        cat > docs-sync/SYNC_INFO.md << EOF
        # Documentation Sync Information

        - **Last synced**: $(date -u '+%Y-%m-%d %H:%M:%S UTC')
        - **Source repository**: danielfbm/private-docs
        - **Source commit**: [${{ github.sha }}](https://github.com/danielfbm/private-docs/commit/${{ github.sha }})
        - **Triggered by**: ${{ github.actor }}
        - **Workflow run**: [#${{ github.run_number }}](https://github.com/danielfbm/private-docs/actions/runs/${{ github.run_id }})

        ## Files synced:
        EOF

        # List synced files
        echo "- docs/ directory" >> docs-sync/SYNC_INFO.md
        echo "- README.md" >> docs-sync/SYNC_INFO.md

        # Add file count
        if [ -d "docs-sync/docs" ]; then
          file_count=$(find docs-sync/docs -type f | wc -l)
          echo "- Total files: $file_count" >> docs-sync/SYNC_INFO.md
        fi

    - name: Configure Git for docs-sync
      if: steps.check_reverse_sync.outputs.reverse_sync == 'false'
      run: |
        cd docs-sync
        git config user.name "Documentation Sync Bot"
        git config user.email "<EMAIL>"

    - name: Check for changes and commit
      if: steps.check_reverse_sync.outputs.reverse_sync == 'false'
      id: commit
      run: |
        cd docs-sync
        git add .

        # Check if there are changes to commit
        if git diff --staged --quiet && [ "${{ inputs.force_sync }}" != "true" ]; then
          echo "changes=false" >> $GITHUB_OUTPUT
          echo "No changes detected - skipping commit"
        else
          echo "changes=true" >> $GITHUB_OUTPUT

          # Get the commit message from the source repository
          cd ../private-docs
          source_commit_msg=$(git log -1 --pretty=format:"%s")
          source_commit_author=$(git log -1 --pretty=format:"%an")

          cd ../docs-sync

          # Create comprehensive commit message
          cat > commit_message.txt << EOF
        📚 Sync documentation from private-docs

        Source: $source_commit_msg
        Author: $source_commit_author
        Commit: ${{ github.sha }}

        This commit automatically syncs documentation changes from the private-docs repository.

        🔗 View source commit: https://github.com/danielfbm/private-docs/commit/${{ github.sha }}
        🤖 Synced on $(date -u '+%Y-%m-%d %H:%M:%S UTC')
        EOF

          git commit -F commit_message.txt
          rm commit_message.txt
        fi

    - name: Push changes
      if: steps.commit.outputs.changes == 'true' && steps.check_reverse_sync.outputs.reverse_sync == 'false'
      run: |
        base_ref=${{ github.event.ref }}
        # Remove refs/heads/ prefix to get just the branch name
        base_ref=${base_ref#refs/heads/}
        cd docs-sync
        git push -u origin $base_ref
        echo "✅ Successfully pushed changes to docs-sync repository on ref $base_ref"

    - name: Create sync summary
      run: |
        if [ "${{ steps.check_reverse_sync.outputs.reverse_sync }}" == "true" ]; then
          echo "## 🔄 Reverse Sync Detected - Forward Sync Skipped" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "This commit was created by the reverse sync process from docs-sync." >> $GITHUB_STEP_SUMMARY
          echo "Forward sync has been skipped to prevent an infinite sync loop." >> $GITHUB_STEP_SUMMARY
        elif [ "${{ steps.commit.outputs.changes }}" == "true" ]; then
          echo "## 🎉 Sync Completed Successfully" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "Documentation has been successfully synced from private-docs to docs-sync." >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Details:" >> $GITHUB_STEP_SUMMARY
          echo "- **Source commit**: [${{ github.sha }}](https://github.com/danielfbm/private-docs/commit/${{ github.sha }})" >> $GITHUB_STEP_SUMMARY
          echo "- **Target repository**: [danielfbm/docs-sync](https://github.com/danielfbm/docs-sync)" >> $GITHUB_STEP_SUMMARY
          echo "- **Sync time**: $(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
        else
          echo "## ℹ️ No Changes to Sync" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "No changes were detected in the documentation files." >> $GITHUB_STEP_SUMMARY
        fi

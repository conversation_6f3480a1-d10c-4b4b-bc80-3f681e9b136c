---
weight: 40
---

:::tip

You can view a [Release Notes example](https://product-doc-guide.alauda.cn/template/release_notes/example.html) for reference.

:::

# Release Notes

## Minor Version: e.g., 2.0.0

### Features and Enhancements

#### Feature Name
Provide a brief introduction to the new or enhanced features.


### Deprecated

#### Feature Name
Provide a brief introduction to the deprecated features, including reasons for deprecation and suggested alternatives.


### Features to be phased out

#### Feature Name
Provide a brief introduction to the features that will be deprecated, including reasons for deprecation and suggested alternatives.


### Fixed Issues

The list of bug fixes can be automatically generated from JIRA issues using the `release-notes-for-bugs` component provided by the documentation framework.

For usage instructions, refer to the [Guide to Automatically Generating Release Notes](https://product-doc-guide.alauda.cn/guide/auto_gen_releasenotes.html).


### Known Issues

Similar to bug fixes, known issues can be automatically generated from JIRA issues using the `release-notes-for-bugs` component provided by the documentation framework.

For usage instructions, refer to the [Guide to Automatically Generating Release Notes](https://product-doc-guide.alauda.cn/guide/auto_gen_releasenotes.html).
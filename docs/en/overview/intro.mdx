---
weight: 10
---

:::tip

The "Introduction" section serves as the opening of product documentation or a functional module, providing an overall description of the product or function. It offers readers a clear initial understanding.

You can view an [Introduction Document example](https://product-doc-guide.alauda.cn/template/intro/example.html) for reference.

:::

# Introduction

> Apart from the introduction itself, if other sections are too long or relatively complex, they can be separated into independent documents.

This section provides a brief description. Specifically introduce the intended use of [Product/Module Name]. For example:

Container Management is a containerized management module built on Kubernetes for cloud-native applications. It is the core of ACP, based on a native multi-cluster architecture, decoupling the underlying infrastructure to achieve unified management of multi-cloud and multi-cluster environments, significantly simplifying the enterprise's cloud application process and effectively reducing operational management and labor costs. Through container management, you can easily create Kubernetes clusters and quickly build an enterprise-level container cloud management platform.

## Limitations[Optional]

> If there are usage limitations for the [Product/Module], they can be explained here.

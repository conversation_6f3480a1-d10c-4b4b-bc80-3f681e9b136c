---
weight: 30
---

:::tip

Use a "simple and actionable" example to help users quickly experience the key features.

For reference, you can view a [Quick Start example](https://product-doc-guide.alauda.cn/template/quick_start/example.html).

:::

# Quick Start

Summarize the purpose, audience, and main content of this document in one sentence. For example: This document helps new users quickly understand and use the XXX feature.

## Introduction

### Use Cases

Provide users with a simple scenario to help them understand what this document covers and what problems it solves.

### Estimated Reading Time

Indicate the estimated time to read and follow this guide, for example: 10-15 minutes.

## Important Notes (Optional)

Highlight any important considerations users should be aware of before proceeding, such as: permission requirements, usage limitations, important reminders, or potential risks.

## Prerequisites (Optional)

List any preparations users need to make before following this guide, such as: required background knowledge, environment requirements, software versions, hardware configurations, or network requirements.

## Process Overview

- Use a table to list the steps users need to follow, providing an overview of the entire process. The list should include:
  - Step number: Starting from 1 and incrementing sequentially.
  - Operation: The steps users need to perform. All steps should form a complete process, giving users a macro understanding of the feature and clearly showing what results they can expect.
  - Description: A concise explanation of each step that allows users to quickly understand what's involved.

## Step-by-Step Instructions

Provide detailed instructions for each step outlined in the Process Overview.

### Step 1

- Begin with a paragraph summarizing this operation, helping users understand the basic concepts related to this functionality.

- Under the paragraph, use an ordered list to detail the specific configuration steps.

...

### Step N

- Begin with a paragraph summarizing this operation, helping users understand the basic concepts related to this functionality.

- Under the paragraph, use an ordered list to detail the specific configuration steps.

### Expected Results (Adjust title based on actual content)

To help users better understand the entire process and functionality, use one or more of the following methods to demonstrate the expected results:

- Feature Overview: Describe the main characteristics and purposes through text.
- Visual Demonstration: Add images to visualize the results.
- Step List: Detail the operation process through a step-by-step list.
apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: to-community-e2e-test
  annotations:
    pipelinesascode.tekton.dev/on-comment: "^/community-e2e$"
    pipelinesascode.tekton.dev/max-keep-runs: "5"
spec:
  timeouts:
    pipeline: 3h
    tasks: 2h

  taskRunSpecs:
    - pipelineTaskName: run-test
      computeResources:
        limits:
          cpu: "4"
          memory: 4Gi
    - pipelineTaskName: git-clone
      computeResources:
        limits:
          cpu: "1"
          memory: 1Gi

  pipelineRef:
    resolver: hub
    params:
    - name: catalog
      value: alauda
    - name: type
      value: tekton
    - name: kind
      value: pipeline
    - name: name
      value: vcluster-integration-test
    - name: version
      value: "0.1"

  params:
    - name: git-revision
      value:
        url: "{{ repo_url }}"
        branch: "{{ source_branch }}"
        commit: "{{ revision }}"
    - name: test
      value:
        image: registry.alauda.cn:60080/devops/tektoncd/e2e/operator/test:latest
        command: |
          set -ex

          echo "set env from secret"
          ls -alh $(workspaces.secret.path)

          cd $(workspaces.source.path)

          # show dependencies
          export PATH=/usr/local/bin/:$PATH
          yq --version
          kustomize version
          kubectl version --client

          export KUBECONFIG=$(workspaces.config.path)/vcluster-config
          kubectl config view
          kubectl get pods -A

          export GOPROXY=https://build-nexus.alauda.cn/repository/golang/,direct
          export GOMAXPROCS=4
          export YQ=$(which yq)
          export KUSTOMIZE=$(which kustomize)

          # deploy the operator
          make apply-patches-default
          make deploy

          # copy the manifests to e2e test
          rm -rf upstream/test/manifests/*
          mkdir -p upstream/test/manifests/
          cp -rf .ko/operator/kodata/* upstream/test/manifests/

          echo "run integration tests..."

          # set the env for e2e tests
          export E2E_SKIP_CLUSTER_CREATION=true
          export E2E_SKIP_OPERATOR_INSTALLATION=true

          cd upstream
          bash -x ./test/e2e-tests.sh
          cd $(workspaces.source.path)

          # set the test result
          echo -n "$( [ $? -ne 0 ] && echo 'failed' || echo 'passed' )" | tee $(results.result.path)

          # Calculate the most recent directory created with mktemp
          target_path=$(mktemp | sed 's/[^/]*$//')
          target_dir=$(ls -artl ${target_path} | grep '^d' | grep 'tmp\.' | tail -n 1 | sed 's/.* //g')

          echo "copy report files from ${target_path}/${target_dir} to local test-reports"
          cp -rf ${target_path}/${target_dir} ./test-reports

          # 因为测试报告收集时暂时只支持 allure-results 并不支持 allure-report，所以这里暂时不生成 allure-report
          allure generate --clean ./test-reports -o allure-report.raw
          mv allure-report.raw ./test-reports/

    - name: report
      value:
        image: build-harbor.alauda.cn/devops/test-plumbing:main
        command: |
          set -ex

          cd $(workspaces.source.path)
          python /app/scripts/log_to_allure.py \
            --logs ${LOG_DIR} \
            --epic "tektoncd-operator" \
            --feature "官方集成测试" \
            --status ${TEST_STATUS}

          allure generate --clean

          # save the logs to report
          mv test-reports ./allure-report/e2e.logs

  workspaces:
    - name: kube-config
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 50Mi
    - name: cache
      persistentVolumeClaim:
        claimName: build-cache
      subPath: golang
    - name: source
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 1Gi
    - name: upload-conf
      secret:
        secretName: upload-allure-report-conf
    - name: test-config
      secret:
        secretName: pac-e2e-config

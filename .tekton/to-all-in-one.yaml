apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: to-all-in-one
  annotations:
    pipelinesascode.tekton.dev/on-comment: "^(/allinone)$"
    pipelinesascode.tekton.dev/on-cel-expression: |-
      (
        source_branch.matches("^(main|master|release-.*)$") ||
        target_branch.startsWith("refs/tags/") ||
        !last_commit_title.contains("Auto-commit")
      ) && ((
        event == "push" && (
          source_branch.matches("^(main|master|release-.*)$") ||
          target_branch.matches("^(main|master|release-.*)$") ||
          target_branch.startsWith("refs/tags/")
        )
      ) || (
        event == "pull_request" && (
          target_branch.matches("^(main|master|release-.*)$")
        )
      ))
    pipelinesascode.tekton.dev/max-keep-runs: "20"
    # 传递给 golang-check 该 PipelineInPipeline 的配置
    pip.tekton.dev/runspec.golang-check: |
      taskRunSpecs:
        - pipelineTaskName: go-test
          computeResources:
            limits:
              cpu: '4'
              memory: 4Gi
            requests:
              cpu: '2'
              memory: 2Gi
        - pipelineTaskName: golangci-lint
          computeResources:
            limits:
              cpu: '4'
              memory: 4Gi
            requests:
              cpu: '2'
              memory: 2Gi
        - pipelineTaskName: sonarqube-scanner
          computeResources:
            limits:
              cpu: '2'
              memory: 4Gi
            requests:
              cpu: '1'
              memory: 2Gi
        - pipelineTaskName: govulncheck
          computeResources:
            requests:
              cpu: "8"
              memory: 10Gi
            limits:
              cpu: "8"
              memory: 10Gi
spec:
  timeouts:
    pipeline: 3h
    tasks: 1h

  params:
    - name: git-url
      value: "{{ repo_url }}"
    - name: git-revision
      value: "{{ source_branch }}"
    - name: git-commit
      value: "{{ revision }}"
    - name: target-branch
      value: "{{ target_branch }}"
    - name: pull-request-number
      value: "{{ pull_request_number }}"
    # 用于更新 values.yaml 中指定制品的版本
    # 例如: ".global.images.ui.tag=latest,.global.images.api.tag=latest"
    - name: overwrite-artifacts
      value: "{{ overwrite_artifacts }}"
    # 于强制构建的组件名称，多个之间用逗号分隔。all 则触发所有组件构建
    - name: force-build-components
      value: "{{ force_build_components }}"

  pipelineSpec:
    description: The pipeline is used to combine multiple pipeline templates
    params:
      - name: git-url
        description: Repository URL to clone from.
        type: string
      - name: git-revision
        description: Revision to checkout. (branch, tag, sha, ref, etc...)
        type: string
      - name: git-commit
        description: Commit sha used in this build
        default: ""
      - name: target-branch
        description: The target branch of the pull request
        type: string
        default: ""
      - name: pull-request-number
        description: Pull request number
        default: ""
      - name: go-environments
        type: array
        default:
          - GOPROXY=https://build-nexus.alauda.cn/repository/golang/,https://goproxy.cn,direct
          - GOMAXPROCS=4
          - GOFLAGS=-buildvcs=false
          - CGO_ENABLED=0

    results:
      - name: ociContainerImageBuild-url
        type: string
        value: $(tasks.build-bundle-image.results.ociContainerImageBuild-url)
        description: Target bundle image address and tag

    tasks:
      - name: git-clone
        timeout: 30m
        retries: 3
        taskRef:
          resolver: hub
          params:
            - name: catalog
              value: catalog
            - name: kind
              value: task
            - name: name
              value: git-clone
            - name: version
              value: "0.9"

        params:
          - name: url
            value: $(params.git-url)
          - name: revision
            value: $(params.git-revision)

        workspaces:
          - name: output
            workspace: source
          - name: basic-auth
            workspace: basic-auth

      - name: detect-component-changes
        runAfter:
          - git-clone
        timeout: 60m
        retries: 0
        params:
          - name: image
            value: registry.alauda.cn:60080/devops/nonroot/chainguard/git:latest
          - name: imagePullPolicy
            value: Always
          - name: script
            value: |
              #!/bin/bash
              set -x

              export SOURCE_BRANCH=$(params.git-revision)
              export TARGET_BRANCH=$(params.target-branch)

              export FORCE_BUILD_COMPONENTS=$(params.force-build-components)
              export RESULT_PATH=$(results.object-result.path)

              export WORKSPACE_SOURCE_PATH=$(workspaces.source.path)
              export WORKSPACE_SECRET_BOUND=$(workspaces.secret.bound)
              export WORKSPACE_SECRET_PATH=$(workspaces.secret.path)

              # Define components and their corresponding check paths
              COMMON_PATHS="upstream .tekton/patches/ .tekton/to-all-in-one.yaml"
              declare -A components=(
                ["tkn"]=".tekton/to-all-in-one.yaml .tekton/dockerfiles/tkn.Dockerfile"
                ["proxy"]="${COMMON_PATHS} .tekton/dockerfiles/proxy.Dockerfile"
                ["webhook"]="${COMMON_PATHS} .tekton/dockerfiles/webhook.Dockerfile"
                ["operator"]="${COMMON_PATHS} .tekton/dockerfiles/operator.Dockerfile .ko/operator/**"
              )

              # Export components to be used in the script
              export COMPONENTS_DEF=$(declare -p components)

              # Commit the changes
              calculate-changed-components.sh

              # The results of the script are stored in the object-result
              # {
              #   "message": "",                # Reserved field
              #   "any": true,                  # true indicates at least one component has changes
              #   "all": true,                  # true indicates all components have changes
              #   "changed_{key}": "true"       # {key} is the component name in components map, true indicates this component has changes
              # }

        taskRef:
          resolver: hub
          params:
            - name: catalog
              value: catalog
            - name: kind
              value: task
            - name: name
              value: run-script
            - name: version
              value: "0.1"
        workspaces:
          - name: source
            workspace: source
          - name: secret
            workspace: basic-auth

      - name: prepare-go
        runAfter:
          - detect-component-changes
        when:
          - input: $(tasks.detect-component-changes.results.object-result.any)
            operator: in
            values:
              - "true"
        taskRef:
          resolver: hub
          params:
            - name: catalog
              value: extras
            - name: kind
              value: task
            - name: name
              value: golang-build
            - name: version
              value: "0.4"
        workspaces:
          - name: source
            workspace: source
          - name: cache
            workspace: go-cache
          - name: basic-auth
            workspace: basic-auth
        params:
          - name: image
            value: registry.alauda.cn:60080/devops/nonroot/builder-go:latest
          - name: environments
            value: $(params.go-environments[*])
          - name: command
            value: |
              # Apply patches and upgrade go dependencies
              make apply-patches upgrade-go-dependencies

              # generate HEAD and show the diff
              cd upstream

              # 组件构建时依赖了该文件
              git rev-parse HEAD > ../head && cat ../head

              echo "=> git diff go.mod"
              time git diff go.mod

      - name: golang-check
        runAfter:
          - prepare-go
        timeout: 60m
        when:
          - input: $(tasks.detect-component-changes.results.object-result.any)
            operator: in
            values:
              - "true"
        retries: 1
        taskRef:
          apiVersion: tekton.dev/v1
          kind: Pipeline
          name: golang-controller-pipeline
          resolver: hub
          params:
            - name: catalog
              value: alauda
            - name: type
              value: tekton
            - name: kind
              value: pipeline
            - name: name
              value: golang-controller-pipeline
            - name: version
              value: "0.1"

        params:
          - name: skip-git-clone
            value: "true"
          - name: skip-commit
            value: "true"
          - name: git-url
            value: $(params.git-url)
          - name: git-revision
            value: $(params.git-revision)
          - name: pull-request-number
            value: $(params.pull-request-number)

          - name: sonar-url
            value: https://build-sonar.alauda.cn/

          - name: image-repository
            value: "" # skip build image

          - name: go-environments
            value: $(params.go-environments[*])

          - name: go-test-command
            value: |
              set -x
              cd upstream
              go mod download -x

              COVER_PROFILE="cover.out"
              TEST_FILE="test.json"
              GO_TEST_FLAGS="-v -json"

              time go test ${GO_TEST_FLAGS} -tags=${GO_TEST_TAGS} -coverpkg=./... -coverprofile ${COVER_PROFILE} ./... | tee ${TEST_FILE}

          - name: cilint-command
            value: |
              cd upstream
              time golangci-lint run --timeout=30m --concurrency=4 --issues-exit-code=0 -v

          - name: go-build-test-image
            value: build-harbor.alauda.cn/devops/nonroot/builder-go:latest

          - name: vulncheck-command
            value: |
              mkdir -p bin
              export VULNCHECK_DIR=upstream
              export VULNCHECK_OUTPUT=bin/vulncheck.txt
              # do not throw an error here immediately, but check whether it should fail in the subsequent logic
              make vulncheck || true

              # count means the line number which contains 'Fixed in' and has fix version
              count=$(grep 'Fixed in' "$VULNCHECK_OUTPUT" | grep -v 'Fixed in: N/A' | wc -l)
              if [ "$count" -gt 0 ]; then
                echo "==> ❌ there are $count vulnerability need to be fixed"
                exit 1
              else
                echo "==> ✅ there is no vulnerability need to be fixed"
              fi
              # TODO: Add report back to PR

              cat ./bin/vulncheck.txt

          - name: ignore-trivy-scan
            value: "true"

        workspaces:
          - name: source
            workspace: source
          - name: dockerconfig
            workspace: dockerconfig
          - name: basic-auth
            workspace: basic-auth
          - name: gitversion-config
            workspace: gitversion-config
          - name: sonar-credentials
            workspace: sonar-credentials
          - name: go-cache
            workspace: go-cache

      - name: build-tkn-image
        runAfter:
          - prepare-go
        timeout: 60m
        retries: 1
        when:
          - input: $(tasks.detect-component-changes.results.object-result.changed_tkn)
            operator: in
            values:
              - "true"
        taskRef:
          apiVersion: tekton.dev/v1
          kind: Pipeline
          name: clone-image-build-test-scan
          resolver: hub
          params:
            - name: catalog
              value: alauda
            - name: type
              value: tekton
            - name: kind
              value: pipeline
            - name: name
              value: clone-image-build-test-scan
            - name: version
              value: "0.2"

        params:
          - name: skip-git-clone
            value: "true"
          - name: skip-commit
            value: "true"
          - name: git-url
            value: $(params.git-url)
          - name: git-revision
            value: $(params.git-revision)

          - name: image-repository
            value: build-harbor.alauda.cn/devops/tektoncd/dogfooding/tkn

          - name: dockerfile-path
            value: .tekton/dockerfiles/tkn.Dockerfile

          - name: context
            value: "."

          - name: file-list-for-commit-sha
            value:
              - .tekton/dockerfiles/tkn.Dockerfile
              - .tekton/to-all-in-one.yaml

          - name: update-files-based-on-image
            value: |
              export YQ=$(which yq)

              echo "update_image_version.sh values.yaml ${IMAGE}"
              update_image_version.sh values.yaml ${IMAGE}

          - name: test-script
            value: ""

          - name: prepare-tools-image
            value: registry.alauda.cn:60080/devops/nonroot/builder-go:latest

          - name: prepare-command
            value: |
              #!/bin/bash
              set -ex

              # download tekton cli source code

              export TKN_VERSION="v0.34.0"
              git clone --depth 1 --branch ${TKN_VERSION} https://github.com/tektoncd/cli.git tektoncd-cli

              cd tektoncd-cli

              # avoid effecting the other components
              # git rev-parse HEAD > ../head && cat ../head

              export GOPROXY=https://build-nexus.alauda.cn/repository/golang/,https://goproxy.cn,direct
              export CGO_ENABLED=0
              export GONOSUMDB=*
              export GOMAXPROCS=4

              export GOCACHE=/tmp/.cache/go-build
              mkdir -p $GOCACHE

              # upgrade go mod dependencies
              go get github.com/go-jose/go-jose/v3@v3.0.4
              go get github.com/sigstore/cosign/v2@v2.2.4
              go get golang.org/x/net@v0.40.0
              go get google.golang.org/protobuf@v1.33.0
              go get gopkg.in/go-jose/go-jose.v2@v2.6.3
              go get github.com/hashicorp/go-retryablehttp@v0.7.7
              go get github.com/Azure/azure-sdk-for-go/sdk/azidentity@v1.6.0
              go get github.com/docker/docker@v27.1
              go get golang.org/x/crypto@v0.38.0
              go get github.com/go-jose/go-jose/v4@v4.0.5
              go get github.com/golang-jwt/jwt/v4@v4.5.2
              go get github.com/golang-jwt/jwt/v5@v5.2.2
              go mod tidy
              go mod vendor

              cd ..

        workspaces:
          - name: source
            workspace: source
          - name: dockerconfig
            workspace: dockerconfig
          - name: basic-auth
            workspace: basic-auth
          - name: gitversion-config
            workspace: gitversion-config

      - name: build-proxy-image
        runAfter:
          - prepare-go
        timeout: 60m
        retries: 1
        when:
          - input: $(tasks.detect-component-changes.results.object-result.changed_proxy)
            operator: in
            values:
              - "true"
        taskRef:
          apiVersion: tekton.dev/v1
          kind: Pipeline
          name: clone-image-build-test-scan
          resolver: hub
          params:
            - name: catalog
              value: alauda
            - name: type
              value: tekton
            - name: kind
              value: pipeline
            - name: name
              value: clone-image-build-test-scan
            - name: version
              value: "0.2"

        params:
          - name: skip-git-clone
            value: "true"
          - name: skip-commit
            value: "true"
          - name: git-url
            value: $(params.git-url)
          - name: git-revision
            value: $(params.git-revision)

          - name: image-repository
            value: build-harbor.alauda.cn/devops/tektoncd/operator/cmd/kubernetes/proxy-webhook

          - name: dockerfile-path
            value: .tekton/dockerfiles/proxy.Dockerfile

          - name: context
            value: "."

          - name: file-list-for-commit-sha
            value:
              - upstream
              - .tekton/patches
              - .tekton/dockerfiles/proxy.Dockerfile
              - .tekton/to-all-in-one.yaml

          - name: update-files-based-on-image
            value: |
              export YQ=$(which yq)

              echo "update_image_version.sh values.yaml ${IMAGE}"
              update_image_version.sh values.yaml ${IMAGE}

        workspaces:
          - name: source
            workspace: source
          - name: dockerconfig
            workspace: dockerconfig
          - name: basic-authkk
            workspace: basic-auth
          - name: gitversion-config
            workspace: gitversion-config

      - name: build-webhook-image
        runAfter:
          - prepare-go
        when:
          - input: $(tasks.detect-component-changes.results.object-result.changed_webhook)
            operator: in
            values:
              - "true"
        timeout: 60m
        retries: 1
        taskRef:
          apiVersion: tekton.dev/v1
          kind: Pipeline
          name: clone-image-build-test-scan
          resolver: hub
          params:
            - name: catalog
              value: alauda
            - name: type
              value: tekton
            - name: kind
              value: pipeline
            - name: name
              value: clone-image-build-test-scan
            - name: version
              value: "0.2"

        params:
          - name: skip-git-clone
            value: "true"
          - name: skip-commit
            value: "true"
          - name: git-url
            value: $(params.git-url)
          - name: git-revision
            value: $(params.git-revision)

          - name: image-repository
            value: build-harbor.alauda.cn/devops/tektoncd/operator/cmd/kubernetes/webhook

          - name: dockerfile-path
            value: .tekton/dockerfiles/webhook.Dockerfile

          - name: context
            value: "."

          - name: file-list-for-commit-sha
            value:
              - upstream
              - .tekton/patches
              - .tekton/dockerfiles/webhook.Dockerfile
              - .tekton/to-all-in-one.yaml

          - name: update-files-based-on-image
            value: |
              export YQ=$(which yq)

              echo "update_image_version.sh values.yaml ${IMAGE}"
              update_image_version.sh values.yaml ${IMAGE}

        workspaces:
          - name: source
            workspace: source
          - name: dockerconfig
            workspace: dockerconfig
          - name: basic-auth
            workspace: basic-auth
          - name: gitversion-config
            workspace: gitversion-config

      - name: build-operator-image
        runAfter:
          - prepare-go
        when:
          - input: $(tasks.detect-component-changes.results.object-result.changed_operator)
            operator: in
            values:
              - "true"
        timeout: 60m
        retries: 1
        taskRef:
          apiVersion: tekton.dev/v1
          kind: Pipeline
          name: clone-image-build-test-scan
          resolver: hub
          params:
            - name: catalog
              value: alauda
            - name: type
              value: tekton
            - name: kind
              value: pipeline
            - name: name
              value: clone-image-build-test-scan
            - name: version
              value: "0.2"

        params:
          - name: skip-git-clone
            value: "true"
          - name: skip-commit
            value: "true"
          - name: git-url
            value: $(params.git-url)
          - name: git-revision
            value: $(params.git-revision)

          - name: image-repository
            value: build-harbor.alauda.cn/devops/tektoncd/operator/cmd/kubernetes/operator

          - name: dockerfile-path
            value: .tekton/dockerfiles/operator.Dockerfile

          - name: context
            value: "."

          - name: file-list-for-commit-sha
            value:
              - upstream
              - .tekton/patches
              - .tekton/dockerfiles/operator.Dockerfile
              - .tekton/to-all-in-one.yaml
              - .ko/operator

          - name: update-files-based-on-image
            value: |
              export YQ=$(which yq)

              echo "update_image_version.sh values.yaml ${IMAGE}"
              update_image_version.sh values.yaml ${IMAGE}

          - name: prepare-command
            value: |
              #!/bin/bash
              set -ex

              # Check if there are subdirectories in the same directory as *.yaml files
              # NOTE: tektoncd-operator will not read manifests recursively,
              # so if there are subdirectories in the same directory as *.yaml files,
              # tektoncd-operator will ignore the manifests in subdirectories.
              (
                echo "=> Checking for subdirectories in directories containing yaml files"
                find .ko -type f -name "*.yaml" | while read -r relfile; do
                  dir=$(dirname "$relfile")
                  subdir_count=$(find "$dir" -mindepth 1 -maxdepth 1 -type d | wc -l)
                  if [ "$subdir_count" -gt 0 ]; then
                    echo "ERROR: $dir has both yaml and subdirectories, tektoncd-operator will ignore the manifests in subdirectories, please check and fix"
                    exit 1
                  fi
                done
              )

              # TODO: Remove this block when cyclops supports tag + digest combination.
              # Remove the `@sha256:[0-9a-f]{64}` in all yaml files under the .ko/operator directory
              # This is a limitation of cyclops, which currently does not support the combination of tag + digest in the image replacement whitelist.
              (
                echo "=> Remove the digest in yaml files"
                cd .ko/operator

                # Find all yaml files under the .ko/operator directory
                find . -type f -name "*.yaml" | while read -r file; do
                    echo "Processing: $file"
                    # Use sed to remove the `@sha256:[0-9a-f]{64}` in the yaml file
                    sed -i 's/@sha256:[0-9a-f]\{64\}//g' "$file"
                done
              )

              (
                echo "=> Changing the build-harbor.alauda.cn to registry.alauda.cn:60070"
                cd .ko/operator/kodata

                # Find all yaml files under the .ko/operator directory
                find . -type f -name "*.yaml" | while read -r file; do
                    echo "Processing: $file"
                    # Use sed to replace the `build-harbor.alauda.cn` with `registry.alauda.cn:60070`
                    sed -i 's/build-harbor.alauda.cn/registry.alauda.cn:60070/g' "$file"
                done
              )

              (
                # copy the pipelines-as-code configuration files to the bundle directory
                mkdir -p  .ko/operator/kodata/tekton-addon/pipelines-as-code-templates/
                # Add the file will cause the error that the openshift namespace does not exist after creating the pac. Because the resource is installed in the openshift namespace by default.
                # cp -rf upstream/cmd/openshift/operator/kodata/tekton-addon/pipelines-as-code-templates/* .ko/operator/kodata/tekton-addon/pipelines-as-code-templates/
              )

              echo -e "\n---------------------------\n"
              echo "=> Git diff"
              export HOME=$(mktemp -d)
              git config --global --add safe.directory /workspace/source
              git --no-pager diff .

        workspaces:
          - name: source
            workspace: source
          - name: dockerconfig
            workspace: dockerconfig
          - name: basic-auth
            workspace: basic-auth
          - name: gitversion-config
            workspace: gitversion-config

      - name: commit
        runAfter:
          - golang-check
          - build-tkn-image
          - build-proxy-image
          - build-webhook-image
          - build-operator-image
        when:
          - input: $(tasks.detect-component-changes.results.object-result.any)
            operator: in
            values:
              - "true"
        timeout: 60m
        retries: 1
        taskRef:
          apiVersion: tekton.dev/v1
          kind: Pipeline
          name: clone-update-commit
          resolver: hub
          params:
            - name: catalog
              value: alauda
            - name: type
              value: tekton
            - name: kind
              value: pipeline
            - name: name
              value: clone-update-commit
            - name: version
              value: "0.1"
        params:
          - name: skip-git-clone
            value: "true"
          - name: skip-commit
            value: "false"
          - name: git-url
            value: $(params.git-url)
          - name: git-revision
            value: $(params.git-revision)
          - name: file-list-for-commit-sha
            value:
              - upstream
              - .tekton
              - .ko
              - hack
          - name: update
            value: |
              export YQ=$(which yq)

              # get current version, and remove the -.* suffix
              OLD_VERSION=$(yq eval '.global.version' values.yaml)
              # use the short commit sha as the version suffix
              export SUFFIX=${LAST_CHANGED_COMMIT:0:7}
              echo "update component version ${OLD_VERSION} suffix to ${SUFFIX}"
              make update-component-version

              echo "replace images in release/release.yaml"
              replace_images_by_values.sh release/release.yaml

          - name: pre-commit-script
            value: |
              # reset the sonar-project.properties changes by ci
              git checkout sonar-project.properties

              # revert the changes in the .ko/operator directory
              git checkout .ko/operator/kodata
              #
              # remove head file
              rm -f head
              #
              # revert upstream directory avoid unnecessary changes
              cd upstream
              git checkout .
              cd .. # go back to the root directory

        workspaces:
          - name: source
            workspace: source
          - name: dockerconfig
            workspace: dockerconfig
          - name: basic-auth
            workspace: basic-auth
          - name: gitversion-config
            workspace: gitversion-config

      - name: build-bundle-image
        runAfter:
          - commit
        timeout: 60m
        retries: 1
        taskRef:
          apiVersion: tekton.dev/v1
          kind: Pipeline
          name: build-bundle-image
          resolver: hub
          params:
            - name: catalog
              value: alauda
            - name: type
              value: tekton
            - name: kind
              value: pipeline
            - name: name
              value: build-bundle-image
            - name: version
              value: "0.1"

        params:
          - name: skip-git-clone
            value: "true"
          - name: git-url
            value: $(params.git-url)
          - name: git-revision
            value: $(params.git-revision)
          - name: overwrite-artifacts
            value: $(params.overwrite-artifacts)

          - name: dockerfile-path
            value: upstream/operatorhub/kubernetes/release-artifacts/bundle.Dockerfile

          - name: context
            value: upstream/operatorhub/kubernetes/release-artifacts

          - name: file-list-for-commit-sha
            value:
              - .

          # Prepare
          - name: prepare-tools-image
            value: build-harbor.alauda.cn/devops/nonroot/tektoncd-bundle-builder:v0.70.x

          - name: prepare-command
            value: |
              export VERSION=${VERSION}

              yq() {
                  # this yq is v4.44.6+
                  /usr/local/bin/yq "$@"
              }
              yq --version

              # TODO: Remove this block when cyclops supports tag + digest combination.
              # Remove the `@sha256:[0-9a-f]{64}` in all yaml files under the .ko/operator directory
              # This is a limitation of cyclops, which currently does not support the combination of tag + digest in the image replacement whitelist.
              (
                echo "=> Remove the digest in yaml files"
                cd release

                # Find all yaml files under the .ko/operator directory
                find . -type f -name "*.yaml" | while read -r file; do
                    echo "Processing: $file"
                    # Use sed to remove the `@sha256:[0-9a-f]{64}` in the yaml file
                    sed -i 's/@sha256:[0-9a-f]\{64\}//g' "$file"
                done
              )

              # Update images in values.yaml, if there is no `support_arm` field, add `support_arm: true`
              # By default, images should support multi architectures.
              # If there is any image that does not support arm, please update `support_arm: false` manually to avoid automatic error.
              (
                echo "=> Update images in values.yaml"
                yq e -i '.global.images[] |= select(has("support_arm") | not) += {"support_arm": true}' values.yaml
              )

              (
                echo "=> Changing the build-harbor.alauda.cn to registry.alauda.cn:60070"
                sed -i 's/build-harbor.alauda.cn/registry.alauda.cn:60070/g' values.yaml
                sed -i 's/build-harbor.alauda.cn/registry.alauda.cn:60070/g' release/release.yaml
              )

              echo -e "\n---------------------------\n"
              echo "=> Git diff"
              git config --global --add safe.directory /workspace/source
              git --no-pager diff .

              # Generate bundle manifests
              cd upstream/operatorhub

              export PATH=/usr/local/bin/:$PATH
              mkdir -p .bin
              cp -rf /usr/local/bin/operator-sdk .bin/

              # limit csv name length to 63
              bundleVersion=${VERSION}
              name="tektoncd-operator"
              length=${#name}
              limit=$((63 - 2 - length))             # tektoncd-operator.v${bundleVersion}
              # If the name length exceeds 63 characters, truncate the excess characters and add an "x" at the end to prevent installation plan failure.
              if [[ ${#bundleVersion} -gt ${limit} ]]; then
                echo "truncate the bundle version '${bundleVersion}' to ${limit} characters"
                # Extract the first limit-1 characters of the string and append the character "x" at the end.
                # Avoid having a "-" at the end of the string.
                bundleVersion=${bundleVersion:0:$((limit - 1))}x
              fi

              # Currently, the `BUNDLE_ARGS` does not support the `--addn-labels` parameter.
              # Ref: https://github.com/tektoncd/operator/pull/2494
              export BUNDLE_ARGS="--workspace kubernetes \
                                  --operator-release-version ${bundleVersion} \
                                  --channels pipelines-4.0,latest \
                                  --default-channel latest \
                                  --fetch-strategy-release-manifest \
                                  --release-manifest $(pwd)/../../release/release.yaml \
                                  --upgrade-strategy-semver \
                                 "

              echo -e "\n---------------------------\n"
              echo "=> Generate bundle manifest"
              make operator-bundle

              # Add labels and annotations to the ClusterServiceVersion
              csv_file="./kubernetes/release-artifacts/bundle/manifests/tektoncd-operator.clusterserviceversion.yaml"
              yq e -i '.metadata.labels."operatorframework.io/arch.amd64"="supported"' ${csv_file}
              yq e -i '.metadata.labels."operatorframework.io/arch.arm64"="supported"' ${csv_file}
              yq e -i '.metadata.labels."cpaas.io/protocol.stack.ipv4"="supported"' ${csv_file}
              yq e -i '.metadata.labels."cpaas.io/protocol.stack.ipv6"="supported"' ${csv_file}
              yq e -i '.metadata.labels."cpaas.io/operator-type"="platform"' ${csv_file}

              yq e -i '.metadata.annotations."provider-type"="platform"' ${csv_file}
              yq e -i '.metadata.annotations."provider"="{\"zh\":\"Alauda\", \"en\": \"Alauda\"}"' ${csv_file}
              yq e -i '.metadata.annotations."operatorframework.io/suggested-namespace"="tekton-operator"' ${csv_file}
              yq e -i '.metadata.annotations."categories"="Integration & Delivery"' ${csv_file}
              yq e -i '.spec.displayName="Alauda DevOps Pipelines"' ${csv_file}
              yq e -i '.spec.provider.name="Alauda"' ${csv_file}

              # Modify the `createdAt` of the ClusterServiceVersion to reflect the time of the latest commit,
              # thereby guaranteeing the regeneration of the bundle image.
              export LAST_GIT_COMMITTER_DATE=$(cd /workspace/source ; git log -1 --pretty=%ct)
              # Change the ********** to the format of 2025-02-12T09:34:18Z
              export CREATEAT=$(date -u -d @${LAST_GIT_COMMITTER_DATE} +"%Y-%m-%dT%H:%M:%SZ")
              yq e -i ".metadata.annotations.createdAt=\"${CREATEAT}\"" ${csv_file}

              echo -e "\n---------------------------\n"
              echo "=> Git status"
              git status .

              echo -e "\n---------------------------\n"
              echo "=> Git diff"
              git --no-pager diff .

          # Bundle related
          - name: kustomize-version
            value: v5.5
          - name: operator-sdk-version
            value: v1.38
          - name: image-repository
            value: build-harbor.alauda.cn/devops/tektoncd-operator-bundle
          - name: set-skip-range
            value: ">=0.0.0 <${VERSION}"
          - name: build-command
            value: |
              set -e
              export VERSION=${BUNDLE_VERSION}
              export BUNDLE_VERSION=${BUNDLE_VERSION}

              git config --global --add safe.directory /workspace/source
              # Set the path of the executable file to avoid reinstallation every time.
              # /go/bin/yq4 是 v4.9.0，而 /usr/local/bin/yq 是 v4.44.6。前者输出 json 是 -j 后者是 -o=json 。
              export YQ=/usr/local/bin/yq
              # make --debug update_csv

              make update_ui_annotations
          - name: bundle-version
            value: "${VERSION}"
          - name: csv-file-path
            value: upstream/operatorhub/kubernetes/release-artifacts/bundle/manifests/tektoncd-operator.clusterserviceversion.yaml
          - name: bundle-csv-file-path
            value: upstream/operatorhub/kubernetes/release-artifacts/bundle/manifests/tektoncd-operator.clusterserviceversion.yaml
          - name: check-support-arm
            value: "true"
          - name: values-file-paths
            value:
              - ./values.yaml

          # Trigger Pipeline Related
          - name: upstreams
            value:
              - |
                repo-url: https://github.com/AlaudaDevops/devops-artifact.git
                branch-name: {{ source_branch }}
                yaml-file-path: ./values.yaml

          - name: upstream-branch-condition
            value: "^(main|master|release-.*)$"

          - name: upstream-secret
            value: github-credentials

        workspaces:
          - name: source
            workspace: source
          - name: dockerconfig
            workspace: dockerconfig
          - name: basic-auth
            workspace: basic-auth
          - name: gitversion-config
            workspace: gitversion-config

    workspaces:
      - name: source
        description: Workspace for shared code source
      - name: dockerconfig
        description: >-
          Workspace for Docker configuration files, such as `config.json` or `.dockerconfigjson`.
          This is optional and is used for authentication when pushing images to the registry.
        # 该配置会触发 tekton pipeline 的 bug 导致 controller panic，所以先都设置为必填的。
        # 详见: https://github.com/tektoncd/pipeline/issues/8561
        # optional: true
      # - name: cache
      #   description: Trivy image cache (optional)
      #   optional: true
      - name: basic-auth
        # optional: true
        description: >
          A Workspace containing a .gitconfig and .git-credentials file. These
          will be copied to the user's home before any git commands are run. Any
          other files in this Workspace are ignored. It is strongly recommended
          to use ssh-directory over basic-auth whenever possible and to bind a
          Secret to this Workspace over other volume types.
      - name: gitversion-config
        # optional: true
        description: >
          A Workspace containing a gitversion-config file. This file is used to set the git version.

  workspaces:
    - name: source
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 1Gi
    - name: dockerconfig
      secret:
        secretName: build-harbor.kauto.docfj
    # This secret will be replaced by the pac controller
    - name: basic-auth
      secret:
        secretName: "{{ git_auth_secret }}"
    - name: gitversion-config
      configMap:
        name: gitversion-config
    - name: sonar-credentials
      secret:
        secretName: sonarqube-credentials
    - name: go-cache
      persistentVolumeClaim:
        claimName: build-cache

  taskRunTemplate:
    # 让所有任务都以非 root 用户运行。
    podTemplate:
      securityContext:
        runAsUser: 65532
        runAsGroup: 65532
        fsGroup: 65532
        fsGroupChangePolicy: "OnRootMismatch"


apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: to-fetch-component-releases
  annotations:
    pipelinesascode.tekton.dev/on-comment: "^((/test-all)|(/fetch-component-releases)|(/test-multi.*\ fetch-component-releases.*))$"
    pipelinesascode.tekton.dev/max-keep-runs: "1"
spec:
  pipelineRef:
    resolver: hub
    params:
    - name: catalog
      value: alauda
    - name: type
      value: tekton
    - name: kind
      value: pipeline
    - name: name
      value: clone-update-commit
    - name: version
      value: "0.1"

  params:
    - name: git-url
      value: "{{ repo_url }}"
    - name: git-revision
      value: "{{ source_branch }}"
    - name: git-commit
      value: "{{ revision }}"
    - name: update-base-image
      value: registry.alauda.cn:60080/devops/nonroot/alauda-docker-buildx:latest
    - name: update
      value: |
        set -e

        if [ "${WORKSPACE_SECRET_BOUND}" = "true" ] ; then
          echo "=> copy git credentials"
          cp "${WORKSPACE_SECRET_PATH}/.git-credentials" "${HOME}/.git-credentials"
          cp "${WORKSPACE_SECRET_PATH}/.gitconfig" "${HOME}/.gitconfig"
          chmod 400 "${HOME}/.git-credentials"
          chmod 400 "${HOME}/.gitconfig"
        fi

        # Add the source directory to the safe.directory list
        git config --global --add safe.directory $(workspaces.source.path)

        # Fetch the releases and store to the .tekton/bundle/kodata/
        echo "=> fetching & patch the releases..."
        fetch-components.sh

        # add the changes to the git staging area
        echo "=> adding the changes to the git staging area..."
        git add values.yaml
        git add .ko/operator/kodata

        # check the changes
        echo "=> checking the changes..."
        git diff .

  workspaces:
    - name: source
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 1Gi
    - name: basic-auth
      secret:
        # need to fetch the other repositorys, so cannot use the temporarily created secret
        secretName: github-credentials

  taskRunTemplate:
    podTemplate:
      securityContext:
        runAsUser: 65532
        runAsGroup: 65532
        fsGroup: 65532
        fsGroupChangePolicy: "OnRootMismatch"

apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: to-build-webhook-image
  annotations:
    pipelinesascode.tekton.dev/on-comment: "^((/test-all)|(/build-webhook-image)|(/test-multi.*\ build-webhook-image.*))$"
    # pipelinesascode.tekton.dev/on-cel-expression: |-
    #   (
    #     ".tekton/to-build-webhook-image.yaml".pathChanged() ||
    #     ".tekton/dockerfiles/webhook.Dockerfile".pathChanged() ||
    #     ".tekton/patches/**".pathChanged() ||
    #     "upstream".pathChanged()
    #   ) && (
    #     !"values.yaml".pathChanged() || source_branch.matches("^(main|master|release-.*)$")
    #   ) &&
    #   ((
    #     event == "push" && (
    #       source_branch.matches("^(main|master|release-.*)$") ||
    #       target_branch.matches("^(main|master|release-.*)$") ||
    #       target_branch.startsWith("refs/tags/")
    #     )
    #   ) || (
    #     event == "pull_request" && (
    #       target_branch.matches("^(main|master|release-.*)$")
    #     )
    #   ))
    pipelinesascode.tekton.dev/max-keep-runs: "5"
spec:
  pipelineRef:
    resolver: hub
    params:
    - name: catalog
      value: alauda
    - name: type
      value: tekton
    - name: kind
      value: pipeline
    - name: name
      value: clone-image-build-test-scan
    - name: version
      value: "0.2"

  params:
    - name: git-url
      value: "{{ repo_url }}"
    - name: git-revision
      value: "{{ source_branch }}"
    - name: git-commit
      value: "{{ revision }}"

    - name: image-repository
      value: build-harbor.alauda.cn/devops/tektoncd/operator/cmd/kubernetes/webhook

    - name: dockerfile-path
      value: .tekton/dockerfiles/webhook.Dockerfile

    - name: context
      value: "."

    - name: file-list-for-commit-sha
      value:
        - upstream
        - .tekton/patches
        - .tekton/dockerfiles/webhook.Dockerfile
        - .tekton/to-build-webhook-image.yaml

    - name: update-files-based-on-image
      value: |
        export YQ=$(which yq)

        echo "update_image_version.sh values.yaml ${IMAGE}"
        update_image_version.sh values.yaml ${IMAGE}

        echo "replace images in release/release.yaml"
        replace_images_by_values.sh release/release.yaml webhook

    - name: test-script
      value: ""

    - name: prepare-tools-image
      value: "build-harbor.alauda.cn/devops/nonroot/builder-go:latest"

    - name: prepare-command
      value: |
        #!/bin/bash
        set -ex

        # 生成 head 文件，内容是 upstream 目录的 commit sha
        cd upstream

        git rev-parse HEAD > ../head && cat ../head

        export GOPROXY=https://build-nexus.alauda.cn/repository/golang/,https://goproxy.cn,direct
        export CGO_ENABLED=0
        export GONOSUMDB=*
        export GOMAXPROCS=4

        export GOCACHE=/tmp/.cache/go-build
        mkdir -p $GOCACHE

        # upgrade go mod dependencies
        # go get github.com/docker/docker@v25.0.7

        # go mod tidy
        # go mod vendor
        # git diff go.mod

    - name: pre-commit-script
      value: |
        # remove head file
        rm -f head
        #
        # revert upstream directory avoid unnecessary changes
        cd upstream
        git checkout .
        cd .. # go back to the root directory

  workspaces:
    - name: source
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 1Gi
    - name: dockerconfig
      secret:
        secretName: build-harbor.kauto.docfj
    - name: basic-auth
      secret:
        secretName: "{{ git_auth_secret }}"
    - name: gitversion-config
      configMap:
        name: gitversion-config

  taskRunTemplate:
    podTemplate:
      securityContext:
        runAsUser: 65532
        runAsGroup: 65532
        fsGroup: 65532
        fsGroupChangePolicy: "OnRootMismatch"

  taskRunSpecs:
    - pipelineTaskName: prepare-build
      computeResources:
        limits:
          cpu: '4'
          memory: 4Gi
        requests:
          cpu: '2'
          memory: 2Gi

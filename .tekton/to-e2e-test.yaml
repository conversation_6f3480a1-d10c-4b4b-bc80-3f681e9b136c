apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: to-e2e-test
  annotations:
    pipelinesascode.tekton.dev/on-comment: "^(/e2e)$"
    pipelinesascode.tekton.dev/max-keep-runs: "5"
spec:
  taskRunSpecs:
    - pipelineTaskName: run-test
      computeResources:
        limits:
          cpu: "4"
          memory: 4Gi
    - pipelineTaskName: generate-report
      computeResources:
        limits:
          cpu: "2"
          memory: 512Mi
  pipelineRef:
    resolver: hub
    params:
      - name: catalog
        value: alauda
      - name: type
        value: tekton
      - name: kind
        value: pipeline
      - name: name
        value: vcluster-integration-test
      - name: version
        value: "0.1"

  params:
    - name: git-revision
      value:
        url: "{{ repo_url }}"
        branch: "{{ source_branch }}"
        commit: "{{ revision }}"
    - name: prepare
      value:
        image: registry.alauda.cn:60080/fundamentals/katanomi-ci-builder:master
        command: |
          set -x
          cd $(workspaces.source.path)

          # get config.yaml from configmap of running cluster
          kubectl get configmap release-e2e-test-config -o jsonpath='{.data.config\.yaml}' > ./testing/config.yaml

          echo "==> get config.yaml from configmap of running cluster"
          cat ./testing/config.yaml

          echo -e "\n\n\n"

          export KUBECONFIG=$(workspaces.custom.path)/vcluster-config
          devspace run install-dependencies
    - name: test
      value:
        image: registry.alauda.cn:60080/devops/builder-go:latest
        command: |
          set -x
          cd $(workspaces.source.path)

          # change apt source to alauda internal mirror
          sed -i 's#http://mirrors.tuna.tsinghua.edu.cn#https://internal-mirrors.alauda.cn/repository/cloud#g' /etc/apt/sources.list.d/ubuntu.sources

          # Because the script in e2e test uses the netstat command, net-tools needs to be installed.
          apt-get update && apt-get install -y net-tools skopeo postgresql-client

          # install kubectl and yq
          curl --retry 6 --create-dirs -fLo /tools/bin/kubectl "https://dl.k8s.io/release/v1.31.2/bin/linux/amd64/kubectl"
          curl --retry 6 --create-dirs -fLo /tools/bin/yq "https://github.com/mikefarah/yq/releases/download/v4.44.3/yq_linux_amd64"
          curl --retry 6 --create-dirs -fLo /tools/bin/cosign "https://github.com/sigstore/cosign/releases/download/v2.4.3/cosign-linux-amd64"

          chmod +x /tools/bin/*
          export PATH=/tools/bin:$PATH
          export YQ=/tools/bin/yq

          # sync test image to registry
          REGISTRY_HOST=$(yq '.toolchains.harbor.host' ./testing/config.yaml)
          REGISTRY_PORT=$(yq '.toolchains.harbor.port' ./testing/config.yaml)
          REGISTRY_USERNAME=$(yq '.toolchains.harbor.username' ./testing/config.yaml)
          REGISTRY_PASSWORD=$(yq '.toolchains.harbor.password' ./testing/config.yaml)
          REGISTRY_TEST_URL=$(yq '.registry.test' ./testing/config.yaml)
          skopeo copy docker://${REGISTRY_TEST_URL}/ops/busybox:latest \
            docker://$REGISTRY_HOST:${REGISTRY_PORT}/ops/busybox:latest \
            --all \
            --dest-tls-verify=false \
            --dest-username=${REGISTRY_USERNAME} \
            --dest-password=${REGISTRY_PASSWORD}

          export KUBECONFIG=$(workspaces.config.path)/vcluster-config
          kubectl config view

          export GOPROXY=https://build-nexus.alauda.cn/repository/golang/,https://goproxy.cn,direct
          export GOMAXPROCS=4

          echo "run e2e tests..."
          export REPORT=allure
          export TAGS="@e2e"
          make e2e-test

    - name: report
      value:
        command: |
          cd $(workspaces.source.path)

          mv ./testing/allure-results allure-results
          allure generate --clean

          echo "Listing contents of report directory"
          ls -al
        path: allure-report
  workspaces:
    - name: kube-config
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 50Mi
    - name: cache
      persistentVolumeClaim:
        claimName: build-cache
      subPath: golang
    - name: source
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 1Gi
    - name: upload-conf
      secret:
        secretName: upload-allure-report-conf

apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: to-build-operator-image
  annotations:
    pipelinesascode.tekton.dev/on-comment: "^((/test-all)|(/build-operator-image)|(/test-multi.*\ build-operator-image.*))$"
    # pipelinesascode.tekton.dev/on-cel-expression: |-
    #   (
    #     ".tekton/to-build-operator-image.yaml".pathChanged() ||
    #     ".tekton/dockerfiles/operator.Dockerfile".pathChanged() ||
    #     ".ko/operator/**".pathChanged() ||
    #     ".tekton/patches/**".pathChanged() ||
    #     "upstream".pathChanged()
    #   ) && (
    #     !"values.yaml".pathChanged() || source_branch.matches("^(main|master|release-.*)$")
    #   ) &&
    #   ((
    #     event == "push" && (
    #       source_branch.matches("^(main|master|release-.*)$") ||
    #       target_branch.matches("^(main|master|release-.*)$") ||
    #       target_branch.startsWith("refs/tags/")
    #     )
    #   ) || (
    #     event == "pull_request" && (
    #       target_branch.matches("^(main|master|release-.*)$")
    #     )
    #   ))
    pipelinesascode.tekton.dev/max-keep-runs: "5"
spec:
  pipelineRef:
    resolver: hub
    params:
      - name: catalog
        value: alauda
      - name: type
        value: tekton
      - name: kind
        value: pipeline
      - name: name
        value: clone-image-build-test-scan
      - name: version
        value: "0.2"

  params:
    - name: git-url
      value: "{{ repo_url }}"
    - name: git-revision
      value: "{{ source_branch }}"
    - name: git-commit
      value: "{{ revision }}"

    - name: image-repository
      value: build-harbor.alauda.cn/devops/tektoncd/operator/cmd/kubernetes/operator

    - name: dockerfile-path
      value: .tekton/dockerfiles/operator.Dockerfile

    - name: context
      value: "."

    - name: file-list-for-commit-sha
      value:
        - upstream
        - .tekton/patches
        - .ko/operator
        - .tekton/dockerfiles/operator.Dockerfile
        - .tekton/to-build-operator-image.yaml

    - name: update-files-based-on-image
      value: |
        export YQ=$(which yq)

        echo "update_image_version.sh values.yaml ${IMAGE}"
        update_image_version.sh values.yaml ${IMAGE}

        echo "replace images in release/release.yaml"
        replace_images_by_values.sh release/release.yaml operator

        # get current version, and remove the -.* suffix
        OLD_VERSION=$(yq eval '.global.version' values.yaml)
        # use the short commit sha as the version suffix
        export SUFFIX=${LAST_CHANGED_COMMIT:0:7}
        echo "update component version ${OLD_VERSION} suffix to ${SUFFIX}"
        make update-component-version

        # Remove the `@sha256:[0-9a-f]{64}` in all yaml files under the directory
        # This is a limitation of cyclops, which currently does not support the combination of tag + digest in the image replacement whitelist.
        (
          echo "=> Remove the digest in yaml files"
          cd release

          # Find all yaml files under the directory
          find . -type f -name "*.yaml" | while read -r file; do
              echo "Processing: $file"
              # Use sed to remove the `@sha256:[0-9a-f]{64}` in the yaml file
              sed -i 's/@sha256:[0-9a-f]\{64\}//g' "$file"
          done
        )

    - name: test-script
      value: ""

    - name: prepare-tools-image
      value: "build-harbor.alauda.cn/devops/nonroot/builder-go:latest"

    - name: prepare-command
      value: |
        #!/bin/bash
        set -ex

        # TODO: Remove this block when cyclops supports tag + digest combination.
        # Remove the `@sha256:[0-9a-f]{64}` in all yaml files under the .ko/operator directory
        # This is a limitation of cyclops, which currently does not support the combination of tag + digest in the image replacement whitelist.
        (
          echo "=> Remove the digest in yaml files"
          cd .ko/operator

          # Find all yaml files under the .ko/operator directory
          find . -type f -name "*.yaml" | while read -r file; do
              echo "Processing: $file"
              # Use sed to remove the `@sha256:[0-9a-f]{64}` in the yaml file
              sed -i 's/@sha256:[0-9a-f]\{64\}//g' "$file"
          done
        )

        (
          echo "=> Changing the build-harbor.alauda.cn to registry.alauda.cn:60070"
          cd .ko/operator/kodata

          # Find all yaml files under the .ko/operator directory
          find . -type f -name "*.yaml" | while read -r file; do
              echo "Processing: $file"
              # Use sed to replace the `build-harbor.alauda.cn` with `registry.alauda.cn:60070`
              sed -i 's/build-harbor.alauda.cn/registry.alauda.cn:60070/g' "$file"
          done
        )

        (
          # copy the pipelines-as-code configuration files to the bundle directory
          mkdir -p  .ko/operator/kodata/tekton-addon/pipelines-as-code-templates/
          # Add the file will cause the error that the openshift namespace does not exist after creating the pac. Because the resource is installed in the openshift namespace by default.
          # cp -rf upstream/cmd/openshift/operator/kodata/tekton-addon/pipelines-as-code-templates/* .ko/operator/kodata/tekton-addon/pipelines-as-code-templates/
        )

        echo -e "\n---------------------------\n"
        echo "=> Git diff"
        export HOME=$(mktemp -d)
        git config --global --add safe.directory /workspace/source
        git --no-pager diff .

        # 生成 head 文件，内容是 upstream 目录的 commit sha
        cd upstream

        git rev-parse HEAD > ../head && cat ../head

        export GOPROXY=https://build-nexus.alauda.cn/repository/golang/,https://goproxy.cn,direct
        export CGO_ENABLED=0
        export GONOSUMDB=*
        export GOMAXPROCS=4

        export GOCACHE=/tmp/.cache/go-build
        mkdir -p $GOCACHE

        # upgrade go mod dependencies
        # go get github.com/docker/docker@v25.0.7

        # go mod tidy
        # go mod vendor
        # git diff go.mod

    - name: pre-commit-script
      value: |
        # revert the changes in the .ko/operator directory
        git checkout .ko/operator/kodata

        # remove head file
        rm -f head
        #
        # revert upstream directory avoid unnecessary changes
        cd upstream
        git checkout .
        cd .. # go back to the root directory

    # - name: ignore-trivy-scan
    #   value: "true"

  workspaces:
    - name: source
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 1Gi
    - name: dockerconfig
      secret:
        secretName: build-harbor.kauto.docfj
    # This secret will be replaced by the pac controller
    - name: basic-auth
      secret:
        secretName: "{{ git_auth_secret }}"
    - name: gitversion-config
      configMap:
        name: gitversion-config

  taskRunTemplate:
    # 让所有任务都以非 root 用户运行。
    podTemplate:
      securityContext:
        runAsUser: 65532
        runAsGroup: 65532
        fsGroup: 65532
        fsGroupChangePolicy: "OnRootMismatch"

  taskRunSpecs:
    - pipelineTaskName: prepare-build
      computeResources:
        limits:
          cpu: "4"
          memory: 4Gi
        requests:
          cpu: "2"
          memory: 2Gi

apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: to-build-operator-e2e-image
  annotations:
    pipelinesascode.tekton.dev/on-comment: "^((/test-all)|(/build-operator-e2e-image)|(/test-multi.*\ build-operator-e2e-image.*))$"
    pipelinesascode.tekton.dev/on-cel-expression: |-
      (
        source_branch.matches("^(main|master|release-.*)$") ||
        !last_commit_title.contains("Auto-commit")
      ) && (
        ".tekton/to-build-operator-e2e-image.yaml".pathChanged() ||
        ".tekton/dockerfiles/operator-e2e.Dockerfile".pathChanged() ||
        "testing/**".pathChanged()
      ) &&
      ((
        event == "push" && (
          source_branch.matches("^(main|master|release-.*)$") ||
          target_branch.matches("^(main|master|release-.*)$") ||
          target_branch.startsWith("refs/tags/")
        )
      ) || (
        event == "pull_request" && (
          target_branch.matches("^(main|master|release-.*)$")
        )
      ))
    pipelinesascode.tekton.dev/max-keep-runs: "5"
spec:
  pipelineRef:
    resolver: hub
    params:
      - name: catalog
        value: alauda
      - name: type
        value: tekton
      - name: kind
        value: pipeline
      - name: name
        value: clone-image-build-test-scan
      - name: version
        value: "0.2"

  params:
    - name: git-url
      value: "{{ repo_url }}"
    - name: git-revision
      value: "{{ source_branch }}"
    - name: git-commit
      value: "{{ revision }}"

    - name: image-repository
      value: build-harbor.alauda.cn/devops/tektoncd/e2e/operator/operator-e2e

    # - name: tags
    #   value:
    #     - "latest"

    - name: dockerfile-path
      value: .tekton/dockerfiles/operator-e2e.Dockerfile

    - name: context
      value: "."

    - name: file-list-for-commit-sha
      value:
        - .tekton/dockerfiles/operator-e2e.Dockerfile
        - .tekton/to-build-operator-e2e-image.yaml
        - testing

    - name: prepare-tools-image
      value: "build-harbor.alauda.cn/devops/nonroot/builder-go:latest"

    # 测试镜像，先忽略安全问题。
    - name: ignore-trivy-scan
      value: "true"

    # Trigger Pipeline Related
    - name: upstreams
      value:
        - |
          repo-url: https://github.com/AlaudaDevops/devops-artifact.git
          branch-name: {{ source_branch }}
          yaml-file-path: ./values.yaml

    - name: upstream-branch-condition
      value: "^(main|master|release-.*)$"

    - name: upstream-secret
      value: github-credentials

  workspaces:
    - name: source
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 1Gi
    - name: dockerconfig
      secret:
        secretName: build-harbor.kauto.docfj
    # This secret will be replaced by the pac controller
    - name: basic-auth
      secret:
        secretName: "{{ git_auth_secret }}"
    - name: gitversion-config
      configMap:
        name: gitversion-config

  taskRunTemplate:
    # 让所有任务都以非 root 用户运行。
    podTemplate:
      securityContext:
        runAsUser: 65532
        runAsGroup: 65532
        fsGroup: 65532
        fsGroupChangePolicy: "OnRootMismatch"

  taskRunSpecs:
    - pipelineTaskName: prepare-build
      computeResources:
        limits:
          cpu: "4"
          memory: 4Gi
        requests:
          cpu: "2"
          memory: 2Gi
    - pipelineTaskName: image-scan
      computeResources:
        limits:
          cpu: "2"
          memory: 2Gi
        requests:
          cpu: "1"
          memory: 1Gi

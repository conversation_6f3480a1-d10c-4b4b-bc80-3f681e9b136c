apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: to-build-bundle-image
  annotations:
    pipelinesascode.tekton.dev/on-comment: "^((/test-all)|(/build-bundle-image)|(test-multi.*\ build-bundle-image.*))$"
    # pipelinesascode.tekton.dev/on-cel-expression: |-
    #   (
    #     ".tekton/to-build-bundle-image.yaml".pathChanged() ||
    #     "release/**".pathChanged() ||
    #     source_branch.matches("^(main|master|release-.*)$")
    #   ) && ((
    #     event == "push" && (
    #       source_branch.matches("^(main|master|release-.*)$") ||
    #       target_branch.matches("^(main|master|release-.*)$") ||
    #       target_branch.startsWith("refs/tags/")
    #     )
    #   ) || (
    #     event == "pull_request" && (
    #       target_branch.matches("^(main|master|release-.*)$")
    #     )
    #   ))
    pipelinesascode.tekton.dev/max-keep-runs: "5"
spec:
  pipelineRef:
    resolver: hub
    params:
      - name: catalog
        value: alauda
      - name: type
        value: tekton
      - name: kind
        value: pipeline
      - name: name
        value: build-bundle-image
      - name: version
        value: "0.1"

  params:
    - name: git-url
      value: "{{ repo_url }}"
    - name: git-revision
      value: "{{ source_branch }}"
    - name: git-commit
      value: "{{ revision }}"

    # 用于更新 values.yaml 中指定制品的版本
    # 例如: ".global.images.ui.tag=latest,.global.images.api.tag=latest"
    - name: overwrite-artifacts
      value: "{{ overwrite_artifacts }}"

    - name: dockerfile-path
      value: upstream/operatorhub/kubernetes/release-artifacts/bundle.Dockerfile

    - name: context
      value: upstream/operatorhub/kubernetes/release-artifacts

    - name: file-list-for-commit-sha
      value:
        - .

    # Prepare
    - name: prepare-tools-image
      value: build-harbor.alauda.cn/devops/nonroot/tektoncd-bundle-builder:v0.70.x

    - name: prepare-command
      value: |
        export VERSION=${VERSION}

        yq() {
            # this yq is v4.44.6+
            /usr/local/bin/yq "$@"
        }
        yq --version

        # TODO: Remove this block when cyclops supports tag + digest combination.
        # Remove the `@sha256:[0-9a-f]{64}` in all yaml files under the .ko/operator directory
        # This is a limitation of cyclops, which currently does not support the combination of tag + digest in the image replacement whitelist.
        (
          echo "=> Remove the digest in yaml files"
          cd release

          # Find all yaml files under the .ko/operator directory
          find . -type f -name "*.yaml" | while read -r file; do
              echo "Processing: $file"
              # Use sed to remove the `@sha256:[0-9a-f]{64}` in the yaml file
              sed -i 's/@sha256:[0-9a-f]\{64\}//g' "$file"
          done
        )

        # Update images in values.yaml, if there is no `support_arm` field, add `support_arm: true`
        # By default, images should support multi architectures.
        # If there is any image that does not support arm, please update `support_arm: false` manually to avoid automatic error.
        (
          echo "=> Update images in values.yaml"
          yq e -i '.global.images[] |= select(has("support_arm") | not) += {"support_arm": true}' values.yaml
        )

        (
          echo "=> Changing the build-harbor.alauda.cn to registry.alauda.cn:60070"
          sed -i 's/build-harbor.alauda.cn/registry.alauda.cn:60070/g' values.yaml
          sed -i 's/build-harbor.alauda.cn/registry.alauda.cn:60070/g' release/release.yaml
        )

        echo -e "\n---------------------------\n"
        echo "=> Git diff"
        git config --global --add safe.directory /workspace/source
        git --no-pager diff .

        # Generate bundle manifests
        cd upstream/operatorhub

        export PATH=/usr/local/bin/:$PATH
        mkdir -p .bin
        cp -rf /usr/local/bin/operator-sdk .bin/

        # limit csv name length to 63
        bundleVersion=${VERSION}
        name="tektoncd-operator"
        length=${#name}
        limit=$((63 - 2 - length))             # tektoncd-operator.v${bundleVersion}
        # If the name length exceeds 63 characters, truncate the excess characters and add an "x" at the end to prevent installation plan failure.
        if [[ ${#bundleVersion} -gt ${limit} ]]; then
          echo "truncate the bundle version '${bundleVersion}' to ${limit} characters"
          # Extract the first limit-1 characters of the string and append the character "x" at the end.
          # Avoid having a "-" at the end of the string.
          bundleVersion=${bundleVersion:0:$((limit - 1))}x
        fi

        # Currently, the `BUNDLE_ARGS` does not support the `--addn-labels` parameter.
        # Ref: https://github.com/tektoncd/operator/pull/2494
        export BUNDLE_ARGS="--workspace kubernetes \
                            --operator-release-version ${bundleVersion} \
                            --channels pipelines-4.0,latest \
                            --default-channel latest \
                            --fetch-strategy-release-manifest \
                            --release-manifest $(pwd)/../../release/release.yaml \
                            --upgrade-strategy-semver \
                           "

        echo -e "\n---------------------------\n"
        echo "=> Generate bundle manifest"
        make operator-bundle

        # Add labels and annotations to the ClusterServiceVersion
        csv_file="./kubernetes/release-artifacts/bundle/manifests/tektoncd-operator.clusterserviceversion.yaml"
        yq e -i '.metadata.labels."operatorframework.io/arch.amd64"="supported"' ${csv_file}
        yq e -i '.metadata.labels."operatorframework.io/arch.arm64"="supported"' ${csv_file}
        yq e -i '.metadata.labels."cpaas.io/protocol.stack.ipv4"="supported"' ${csv_file}
        yq e -i '.metadata.labels."cpaas.io/protocol.stack.ipv6"="supported"' ${csv_file}
        yq e -i '.metadata.labels."cpaas.io/operator-type"="platform"' ${csv_file}

        yq e -i '.metadata.annotations."provider-type"="platform"' ${csv_file}
        yq e -i '.metadata.annotations."provider"="{\"zh\":\"Alauda\", \"en\": \"Alauda\"}"' ${csv_file}
        yq e -i '.metadata.annotations."operatorframework.io/suggested-namespace"="tekton-operator"' ${csv_file}
        yq e -i '.metadata.annotations."categories"="Integration & Delivery"' ${csv_file}
        yq e -i '.spec.displayName="Alauda DevOps Pipelines"' ${csv_file}
        yq e -i '.spec.provider.name="Alauda"' ${csv_file}

        # Modify the `createdAt` of the ClusterServiceVersion to reflect the time of the latest commit,
        # thereby guaranteeing the regeneration of the bundle image.
        export LAST_GIT_COMMITTER_DATE=$(cd /workspace/source ; git log -1 --pretty=%ct)
        # Change the ********** to the format of 2025-02-12T09:34:18Z
        export CREATEAT=$(date -u -d @${LAST_GIT_COMMITTER_DATE} +"%Y-%m-%dT%H:%M:%SZ")
        yq e -i ".metadata.annotations.createdAt=\"${CREATEAT}\"" ${csv_file}

        echo -e "\n---------------------------\n"
        echo "=> Git status"
        git status .

        echo -e "\n---------------------------\n"
        echo "=> Git diff"
        git --no-pager diff .

    # Bundle related
    - name: kustomize-version
      value: v5.5
    - name: operator-sdk-version
      value: v1.38
    - name: image-repository
      value: build-harbor.alauda.cn/devops/tektoncd-operator-bundle
    - name: set-skip-range
      value: ">=0.0.0 <${VERSION}"
    - name: build-command
      value: |
        set -e
        export VERSION=${BUNDLE_VERSION}
        export BUNDLE_VERSION=${BUNDLE_VERSION}

        git config --global --add safe.directory /workspace/source
        # Set the path of the executable file to avoid reinstallation every time.
        # /go/bin/yq4 是 v4.9.0，而 /usr/local/bin/yq 是 v4.44.6。前者输出 json 是 -j 后者是 -o=json 。
        export YQ=/usr/local/bin/yq
        # make --debug update_csv

        make update_ui_annotations
    - name: bundle-version
      value: "${VERSION}"
    - name: csv-file-path
      value: upstream/operatorhub/kubernetes/release-artifacts/bundle/manifests/tektoncd-operator.clusterserviceversion.yaml
    - name: bundle-csv-file-path
      value: upstream/operatorhub/kubernetes/release-artifacts/bundle/manifests/tektoncd-operator.clusterserviceversion.yaml
    - name: check-support-arm
      value: "true"
    - name: values-file-paths
      value:
        - ./values.yaml

    # Trigger Pipeline Related
    - name: upstreams
      value:
        - |
          repo-url: https://github.com/AlaudaDevops/devops-artifact.git
          branch-name: {{ source_branch }}
          yaml-file-path: ./values.yaml

    - name: upstream-branch-condition
      value: "^(main|master|release-.*)$"

    - name: upstream-secret
      value: github-credentials

  workspaces:
    - name: source
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 1Gi
    - name: dockerconfig
      secret:
        secretName: build-harbor.kauto.docfj
    # This secret will be replaced by the pac controller
    - name: basic-auth
      secret:
        secretName: "{{ git_auth_secret }}"
    - name: gitversion-config
      configMap:
        name: gitversion-config

  taskRunTemplate:
    # 让所有任务都以非 root 用户运行。
    podTemplate:
      securityContext:
        runAsUser: 65532
        runAsGroup: 65532
        fsGroup: 65532
        fsGroupChangePolicy: "OnRootMismatch"

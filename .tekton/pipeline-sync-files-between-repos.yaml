apiVersion: tekton.dev/v1
kind: Pipeline
metadata:
  name: sync-files-between-repos
spec:
  params:
    - name: source-repo
      type: string
    - name: target-repo
      type: string
    - name: revision
      type: string
      default: main
    - name: commit-message
      type: string
    - name: source-path
      type: string
      default: "source-repo"
    - name: target-path
      type: string
      default: "target-repo"
    - name: matching-files
      type: array
      default:
      - docs/
      - .yarn
      - global.d.ts
      - package.json
      - doom.config.yml
      - yarn.lock
  workspaces:
  - name: source
    optional: false
    description: "Source repository workspace to share files across tasks"
  - name: basic-auth-source
    optional: false
    description: Basic auth secret for source repository
  - name: basic-auth-target
    optional: true
    description: Basic auth secret for target repository. If not specified will use the same as source

  tasks:
  - name: clone-source-repo
    taskRef:
      name: git-clone
    workspaces:
    - name: output
      workspace: source
    - name: basic-auth
      workspace: basic-auth-source
    params:
    - name: url
      value: $(params.source-repo)
    - name: revision
      value: $(params.revision)
    - name: subdirectory
      value: $(params.source-path)
  - name: clone-target-repo
    taskRef:
      name: git-clone
    workspaces:
    - name: output
      workspace: source
    - name: basic-auth
      workspace: basic-auth-target
    params:
    - name: url
      value: $(params.target-repo)
    - name: revision
      value: $(params.revision)
    - name: subdirectory
      value: $(params.target-path)
  - name:

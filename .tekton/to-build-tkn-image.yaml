apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: to-build-tkn-image
  annotations:
    pipelinesascode.tekton.dev/on-comment: "^((/test-all)|(/build-tkn-image)|(/test-multi.*\ build-tkn-image.*))$"
    # pipelinesascode.tekton.dev/on-cel-expression: |-
    #   (
    #     ".tekton/to-build-tkn-image.yaml".pathChanged() ||
    #     ".tekton/dockerfiles/tkn.Dockerfile".pathChanged()
    #   ) && (
    #     !"values.yaml".pathChanged() || source_branch.matches("^(main|master|release-.*)$")
    #   ) &&
    #   ((
    #     event == "push" && (
    #       source_branch.matches("^(main|master|release-.*)$") ||
    #       target_branch.matches("^(main|master|release-.*)$") ||
    #       target_branch.startsWith("refs/tags/")
    #     )
    #   ) || (
    #     event == "pull_request" && (
    #       target_branch.matches("^(main|master|release-.*)$")
    #     )
    #   ))
    pipelinesascode.tekton.dev/max-keep-runs: "5"
spec:
  pipelineRef:
    resolver: hub
    params:
    - name: catalog
      value: alauda
    - name: type
      value: tekton
    - name: kind
      value: pipeline
    - name: name
      value: clone-image-build-test-scan
    - name: version
      value: "0.2"

  params:
    - name: git-url
      value: "{{ repo_url }}"
    - name: git-revision
      value: "{{ source_branch }}"
    - name: git-commit
      value: "{{ revision }}"

    - name: image-repository
      value: build-harbor.alauda.cn/devops/tektoncd/dogfooding/tkn

    - name: dockerfile-path
      value: .tekton/dockerfiles/tkn.Dockerfile

    - name: context
      value: "."

    - name: file-list-for-commit-sha
      value:
        - .tekton/dockerfiles/tkn.Dockerfile
        - .tekton/to-build-tkn-image.yaml

    - name: update-files-based-on-image
      value: |
        export YQ=$(which yq)

        echo "update_image_version.sh values.yaml ${IMAGE}"
        update_image_version.sh values.yaml ${IMAGE}

        echo "replace images in release/release.yaml"
        replace_images_by_values.sh release/release.yaml tkn

    - name: test-script
      value: ""

    - name: prepare-tools-image
      value: "build-harbor.alauda.cn/devops/nonroot/builder-go:latest"

    - name: prepare-command
      value: |
        #!/bin/bash
        set -ex

        # download tekton cli source code

        export TKN_VERSION="v0.34.0"
        git clone --depth 1 --branch ${TKN_VERSION} https://github.com/tektoncd/cli.git tektoncd-cli

        cd tektoncd-cli

        git rev-parse HEAD > ../head && cat ../head

        export GOPROXY=https://build-nexus.alauda.cn/repository/golang/,https://goproxy.cn,direct
        export CGO_ENABLED=0
        export GONOSUMDB=*
        export GOMAXPROCS=4

        export GOCACHE=/tmp/.cache/go-build
        mkdir -p $GOCACHE

        # upgrade go mod dependencies
        go get github.com/go-jose/go-jose/v3@v3.0.3
        go get github.com/sigstore/cosign/v2@v2.2.4
        go get golang.org/x/net@v0.23.0
        go get google.golang.org/protobuf@v1.33.0
        go get gopkg.in/go-jose/go-jose.v2@v2.6.3
        go get github.com/hashicorp/go-retryablehttp@v0.7.7
        go get github.com/Azure/azure-sdk-for-go/sdk/azidentity@v1.6.0
        go get github.com/docker/docker@v27.1
        go get golang.org/x/crypto@v0.31.0
        go get github.com/golang-jwt/jwt/v4@4.5.2
        go get github.com/golang-jwt/jwt/v5@5.2.2
        go mod tidy
        go mod vendor

        cd ..

    - name: pre-commit-script
      value: |
        # remove head file
        rm -f head
        # remove tektoncd-cli directory
        rm -rf tektoncd-cli
        #
        # revert upstream directory avoid unnecessary changes
        cd upstream
        git checkout .
        cd .. # go back to the root directory

  workspaces:
    - name: source
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 1Gi
    - name: dockerconfig
      secret:
        secretName: build-harbor.kauto.docfj
    - name: basic-auth
      secret:
        secretName: "{{ git_auth_secret }}"
    - name: gitversion-config
      configMap:
        name: gitversion-config

  taskRunTemplate:
    podTemplate:
      securityContext:
        runAsUser: 65532
        runAsGroup: 65532
        fsGroup: 65532
        fsGroupChangePolicy: "OnRootMismatch"

  taskRunSpecs:
    - pipelineTaskName: prepare-build
      computeResources:
        limits:
          cpu: '4'
          memory: 4Gi
        requests:
          cpu: '2'
          memory: 2Gi

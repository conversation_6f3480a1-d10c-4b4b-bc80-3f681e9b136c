apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: sync-docs
  annotations:
  pipelinesascode.tekton.dev/on-cel-expression: |-
      (
        "docs/".pathChanged() ||
        "*.ts".pathChanged() ||
        "sites.yaml".pathChanged() ||
        ".yarn/".pathChanged() ||
        "doom.config.yml".pathChanged() ||
        "tsconfig.json".pathChanged()
      ) && (
        !"values.yaml".pathChanged() || source_branch.matches("^(main|master|release-.*)$")
      ) &&
      (
        event == "push" && (
          source_branch.matches("^(main|release-.*)$") ||
          target_branch.matches("^(main|release-.*)$")
        )
      )
spec:
  pipelineRef:
    name: sync-docs
  workspaces:
    - name: source
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteMany
          resources:
            requests:
              storage: 1Gi
    - name: basic-auth
      secret:
        secretName: "{{ git_auth_secret }}"
  params:
    - name: source-repo
# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@alauda/doom@npm:^0.22.2":
  version: 0.22.2
  resolution: "@alauda/doom@npm:0.22.2"
  dependencies:
    "@inquirer/prompts": "npm:^7.5.0"
    "@openapi-contrib/openapi-schema-to-json-schema": "npm:^5.1.0"
    "@playwright/browser-chromium": "npm:^1.52.0"
    "@rsbuild/plugin-yaml": "npm:^1.0.2"
    "@rspress/core": "npm:1.43.12"
    "@shikijs/transformers": "npm:^3.4.0"
    chokidar: "npm:^4.0.3"
    cli-progress: "npm:^3.12.0"
    clsx: "npm:^2.1.1"
    commander: "npm:^13.1.0"
    ejs: "npm:^3.1.10"
    es-toolkit: "npm:^1.37.2"
    html-tag-names: "npm:^2.1.0"
    mermaid: "npm:^11.6.0"
    openai: "npm:^5.0.0-beta.0"
    openapi-types: "npm:^12.1.3"
    p-ratelimit: "npm:^1.0.1"
    pdf-lib: "npm:^1.17.1"
    pdf-merger-js: "npm:^5.1.2"
    picomatch: "npm:^4.0.2"
    playwright: "npm:^1.52.0"
    react-markdown: "npm:^8.0.7"
    rehype-raw: "npm:^6.1.1"
    remark-directive: "npm:^2.0.1"
    remark-frontmatter: "npm:^4.0.1"
    remark-gfm: "npm:^3.0.1"
    shiki: "npm:^3.4.0"
    simple-git: "npm:^3.27.0"
    swagger2openapi: "npm:^7.0.8"
    tinyglobby: "npm:^0.2.13"
    type-fest: "npm:^4.41.0"
    x-fetch: "npm:^0.2.6"
    yaml: "npm:^2.7.1"
    yoctocolors: "npm:^2.1.1"
  bin:
    doom: lib/cli/index.js
  checksum: 10c0/503eeb16fed6873c1f9a00026597256f18f8182ab980ba0a3aed948388983dea3147ec9bb46929be727de001c6b1a7a602f60e8986043da2faf585367a72e52f
  languageName: node
  linkType: hard

"@antfu/install-pkg@npm:^1.0.0":
  version: 1.0.0
  resolution: "@antfu/install-pkg@npm:1.0.0"
  dependencies:
    package-manager-detector: "npm:^0.2.8"
    tinyexec: "npm:^0.3.2"
  checksum: 10c0/2361383f9aef51f39e96d0276eb266f01d1cabd4881bba6db2e3dff392ac33b537fcb18a07c66ecd315b808b9a70dc48a95e53531d407b2e1956f49f3b6c5b5b
  languageName: node
  linkType: hard

"@antfu/utils@npm:^8.1.0":
  version: 8.1.1
  resolution: "@antfu/utils@npm:8.1.1"
  checksum: 10c0/cd55d322496f0324323a7bd312bbdc305db02f5c74c53d59213a00a7ecfd66926b6755a41f27c6e664a687cd7a967d3a8b12d3ea57f264ae45dd1c5c181f5160
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.12.5, @babel/runtime@npm:^7.3.1":
  version: 7.26.9
  resolution: "@babel/runtime@npm:7.26.9"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 10c0/e8517131110a6ec3a7360881438b85060e49824e007f4a64b5dfa9192cf2bb5c01e84bfc109f02d822c7edb0db926928dd6b991e3ee460b483fb0fac43152d9b
  languageName: node
  linkType: hard

"@braintree/sanitize-url@npm:^7.0.4":
  version: 7.1.1
  resolution: "@braintree/sanitize-url@npm:7.1.1"
  checksum: 10c0/fdfc1759c4244e287693ce1e9d42d649423e7c203fdccf27a571f8951ddfe34baa5273b7e6a8dd3007d7676859c7a0a9819be0ab42a3505f8505ad0eefecf7c1
  languageName: node
  linkType: hard

"@bufbuild/protobuf@npm:^2.0.0":
  version: 2.2.3
  resolution: "@bufbuild/protobuf@npm:2.2.3"
  checksum: 10c0/546c38b924c4a8dd79ec457928cfb99a5aab2945d11f15f4f06894bdc148ea37a1ae8b78cf17de9bb5354cbb896f6af4099073690b5b2089e1b196cec963d6ec
  languageName: node
  linkType: hard

"@chevrotain/cst-dts-gen@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/cst-dts-gen@npm:11.0.3"
  dependencies:
    "@chevrotain/gast": "npm:11.0.3"
    "@chevrotain/types": "npm:11.0.3"
    lodash-es: "npm:4.17.21"
  checksum: 10c0/9e945a0611386e4e08af34c2d0b3af36c1af08f726b58145f11310f2aeafcb2d65264c06ec65a32df6b6a65771e6a55be70580c853afe3ceb51487e506967104
  languageName: node
  linkType: hard

"@chevrotain/gast@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/gast@npm:11.0.3"
  dependencies:
    "@chevrotain/types": "npm:11.0.3"
    lodash-es: "npm:4.17.21"
  checksum: 10c0/54fc44d7b4a7b0323f49d957dd88ad44504922d30cb226d93b430b0e09925efe44e0726068581d777f423fabfb878a2238ed2c87b690c0c0014ebd12b6968354
  languageName: node
  linkType: hard

"@chevrotain/regexp-to-ast@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/regexp-to-ast@npm:11.0.3"
  checksum: 10c0/6939c5c94fbfb8c559a4a37a283af5ded8e6147b184a7d7bcf5ad1404d9d663c78d81602bd8ea8458ec497358a9e1671541099c511835d0be2cad46f00c62b3f
  languageName: node
  linkType: hard

"@chevrotain/types@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/types@npm:11.0.3"
  checksum: 10c0/72fe8f0010ebef848e47faea14a88c6fdc3cdbafaef6b13df4a18c7d33249b1b675e37b05cb90a421700c7016dae7cd4187ab6b549e176a81cea434f69cd2503
  languageName: node
  linkType: hard

"@chevrotain/utils@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/utils@npm:11.0.3"
  checksum: 10c0/b31972d1b2d444eef1499cf9b7576fc1793e8544910de33a3c18e07c270cfad88067f175d0ee63e7bc604713ebed647f8190db45cc8311852cd2d4fe2ef14068
  languageName: node
  linkType: hard

"@exodus/schemasafe@npm:^1.0.0-rc.2":
  version: 1.3.0
  resolution: "@exodus/schemasafe@npm:1.3.0"
  checksum: 10c0/e19397c14db76342154c32a9088536149babfd9b18ecae815add0b2f911d9aa292aa51c6ab33b857b4b6bb371a74ebde845e6f17b2824e73b4e307230f23f86a
  languageName: node
  linkType: hard

"@fastify/busboy@npm:^2.0.0":
  version: 2.1.1
  resolution: "@fastify/busboy@npm:2.1.1"
  checksum: 10c0/6f8027a8cba7f8f7b736718b013f5a38c0476eea67034c94a0d3c375e2b114366ad4419e6a6fa7ffc2ef9c6d3e0435d76dd584a7a1cbac23962fda7650b579e3
  languageName: node
  linkType: hard

"@iconify/types@npm:^2.0.0":
  version: 2.0.0
  resolution: "@iconify/types@npm:2.0.0"
  checksum: 10c0/65a3be43500c7ccacf360e136d00e1717f050b7b91da644e94370256ac66f582d59212bdb30d00788aab4fc078262e91c95b805d1808d654b72f6d2072a7e4b2
  languageName: node
  linkType: hard

"@iconify/utils@npm:^2.1.33":
  version: 2.3.0
  resolution: "@iconify/utils@npm:2.3.0"
  dependencies:
    "@antfu/install-pkg": "npm:^1.0.0"
    "@antfu/utils": "npm:^8.1.0"
    "@iconify/types": "npm:^2.0.0"
    debug: "npm:^4.4.0"
    globals: "npm:^15.14.0"
    kolorist: "npm:^1.8.0"
    local-pkg: "npm:^1.0.0"
    mlly: "npm:^1.7.4"
  checksum: 10c0/926013852cd9d09b8501ee0f3f7d40386dc5ed1cb904869d6502f5ee1a64aee5664e9c00da49d700528d26c4a51ea0cac4f046c4eb281d0f8d54fc5df2f3fd0d
  languageName: node
  linkType: hard

"@inquirer/checkbox@npm:^4.1.5":
  version: 4.1.5
  resolution: "@inquirer/checkbox@npm:4.1.5"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/figures": "npm:^1.0.11"
    "@inquirer/type": "npm:^3.0.6"
    ansi-escapes: "npm:^4.3.2"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/b984fb3ce8af34c327f3a85adcfe9fbd9eaac0c689bb9af79a5d55d508acb01de329747e8c923c9f4962e4006c353ed2dbe79e3fc9ae0f85f5851427dbed75ed
  languageName: node
  linkType: hard

"@inquirer/confirm@npm:^5.1.9":
  version: 5.1.9
  resolution: "@inquirer/confirm@npm:5.1.9"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/e35c134303f8151074479d6704c048676b2684debfde18a46ff0fb7585a3ee31dea551899ddcb48169fbef5dfe64c1948d2d8ac17a6939bedd31bb54c39bbea4
  languageName: node
  linkType: hard

"@inquirer/core@npm:^10.1.10":
  version: 10.1.10
  resolution: "@inquirer/core@npm:10.1.10"
  dependencies:
    "@inquirer/figures": "npm:^1.0.11"
    "@inquirer/type": "npm:^3.0.6"
    ansi-escapes: "npm:^4.3.2"
    cli-width: "npm:^4.1.0"
    mute-stream: "npm:^2.0.0"
    signal-exit: "npm:^4.1.0"
    wrap-ansi: "npm:^6.2.0"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/8d0a3b725e42e40efbdc6ed087283795f1e36e642b119dd7dd3cbf31fce74bdbdb1b987da16159cd2475f45b2ede7e33293ae92bad3ac481832889c230df3fc0
  languageName: node
  linkType: hard

"@inquirer/editor@npm:^4.2.10":
  version: 4.2.10
  resolution: "@inquirer/editor@npm:4.2.10"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
    external-editor: "npm:^3.1.0"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/b0213ad3ef45bc30427def4742db22126a1e6a59923033d21cae216276d8cf85d2af8abe432e5567ea24a7f6a31e23e7014e31308405cde684060b974e454a22
  languageName: node
  linkType: hard

"@inquirer/expand@npm:^4.0.12":
  version: 4.0.12
  resolution: "@inquirer/expand@npm:4.0.12"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/f7abfc09ef942b63504677be5cf6fc443fb8090b5d43f7d2fe09983215cc01c6d82351cd1b596e90723b382a0931c9344d3280d54acf47d898782f4af2030b2e
  languageName: node
  linkType: hard

"@inquirer/figures@npm:^1.0.11":
  version: 1.0.11
  resolution: "@inquirer/figures@npm:1.0.11"
  checksum: 10c0/6270e24eebbe42bbc4e7f8e761e906be66b4896787f31ab3e7484ad271c8edc90bce4ec20e232a5da447aee4fc73803397b2dda8cf645f4f7eea83e773b44e1e
  languageName: node
  linkType: hard

"@inquirer/input@npm:^4.1.9":
  version: 4.1.9
  resolution: "@inquirer/input@npm:4.1.9"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/db2e661ee482f3f27bf8cb77f054f99aba30291bd24d63b28db62204c4c5efc496199a9ddc03d01e0f6e6455d6967efb3ef92d2cd91e672905948c8c978c67a1
  languageName: node
  linkType: hard

"@inquirer/number@npm:^3.0.12":
  version: 3.0.12
  resolution: "@inquirer/number@npm:3.0.12"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/e40726e1c60ba48a374b4867d212bd5e14cb12daae97a6536095906246ba6af91ec7fa68e347ba52607ba5bd84f9e804768d12fbc1250b2cac814187fb5e9628
  languageName: node
  linkType: hard

"@inquirer/password@npm:^4.0.12":
  version: 4.0.12
  resolution: "@inquirer/password@npm:4.0.12"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
    ansi-escapes: "npm:^4.3.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/03257985bbbd813c4f0c412effb691737517e348ca2590558864fe09877080daf90eb9910a60d097048fce9cf0c56a900e8f099854a9ae21512ceaadbd986e01
  languageName: node
  linkType: hard

"@inquirer/prompts@npm:^7.5.0":
  version: 7.5.0
  resolution: "@inquirer/prompts@npm:7.5.0"
  dependencies:
    "@inquirer/checkbox": "npm:^4.1.5"
    "@inquirer/confirm": "npm:^5.1.9"
    "@inquirer/editor": "npm:^4.2.10"
    "@inquirer/expand": "npm:^4.0.12"
    "@inquirer/input": "npm:^4.1.9"
    "@inquirer/number": "npm:^3.0.12"
    "@inquirer/password": "npm:^4.0.12"
    "@inquirer/rawlist": "npm:^4.1.0"
    "@inquirer/search": "npm:^3.0.12"
    "@inquirer/select": "npm:^4.2.0"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/40faf282ec35a2b89258c65f8e55f1e9193a0f8bdfbb724641ce7c7036b4e995a84f685d311807ed18885b8e0231c278a40e90469d829eb165b4993b8edf101b
  languageName: node
  linkType: hard

"@inquirer/rawlist@npm:^4.1.0":
  version: 4.1.0
  resolution: "@inquirer/rawlist@npm:4.1.0"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/0e92e8ee7eebd6b6ba7a81d968701f398dd372638f51dd8e3cb1fd3a03520bc0f713e112488d37fdb813f18928f338d82527c575e18a9bebde7ac3273045898c
  languageName: node
  linkType: hard

"@inquirer/search@npm:^3.0.12":
  version: 3.0.12
  resolution: "@inquirer/search@npm:3.0.12"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/figures": "npm:^1.0.11"
    "@inquirer/type": "npm:^3.0.6"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/ef764f96b561b48e4d9a99716789d1fc0941d40884d1c9fea715c304360b46ec8c6e3edf603f7425a27d7743915564f405a3ccd1a72f0379a714be22887fe6ff
  languageName: node
  linkType: hard

"@inquirer/select@npm:^4.2.0":
  version: 4.2.0
  resolution: "@inquirer/select@npm:4.2.0"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/figures": "npm:^1.0.11"
    "@inquirer/type": "npm:^3.0.6"
    ansi-escapes: "npm:^4.3.2"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/b3cfab393d54e48012336710b8e9267a0dd5551878a7727800da3d78602398720aab8777d5687b2138261fb731b0079d1c3ec0f4d0fee194bb1c4496c97b340b
  languageName: node
  linkType: hard

"@inquirer/type@npm:^3.0.6":
  version: 3.0.6
  resolution: "@inquirer/type@npm:3.0.6"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/92382c1b046559ddb16c53e1353a900a43266566a0d73902e5325433c640b6aaeaf3e34cc5b2a68fd089ff5d8add914d0b9875cdec64f7a09313f9c4420b021d
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@jsonjoy.com/base64@npm:^1.1.1":
  version: 1.1.2
  resolution: "@jsonjoy.com/base64@npm:1.1.2"
  peerDependencies:
    tslib: 2
  checksum: 10c0/88717945f66dc89bf58ce75624c99fe6a5c9a0c8614e26d03e406447b28abff80c69fb37dabe5aafef1862cf315071ae66e5c85f6018b437d95f8d13d235e6eb
  languageName: node
  linkType: hard

"@jsonjoy.com/json-pack@npm:^1.0.3":
  version: 1.1.1
  resolution: "@jsonjoy.com/json-pack@npm:1.1.1"
  dependencies:
    "@jsonjoy.com/base64": "npm:^1.1.1"
    "@jsonjoy.com/util": "npm:^1.1.2"
    hyperdyperid: "npm:^1.2.0"
    thingies: "npm:^1.20.0"
  peerDependencies:
    tslib: 2
  checksum: 10c0/fd0d8baa0c8eba536924540717901e0d7eed742576991033cceeb32dcce801ee0a4318cf6eb40b444c9e78f69ddbd4f38b9eb0041e9e54c17e7b6d1219b12e1d
  languageName: node
  linkType: hard

"@jsonjoy.com/util@npm:^1.1.2, @jsonjoy.com/util@npm:^1.3.0":
  version: 1.5.0
  resolution: "@jsonjoy.com/util@npm:1.5.0"
  peerDependencies:
    tslib: 2
  checksum: 10c0/0065ae12c4108d8aede01a479c8d2b5a39bce99e9a449d235befc753f57e8385d9c1115720529f26597840b7398d512898155423d9859fd638319fb0c827365d
  languageName: node
  linkType: hard

"@kwsites/file-exists@npm:^1.1.1":
  version: 1.1.1
  resolution: "@kwsites/file-exists@npm:1.1.1"
  dependencies:
    debug: "npm:^4.1.1"
  checksum: 10c0/39e693239a72ccd8408bb618a0200e4a8d61682057ca7ae2c87668d7e69196e8d7e2c9cde73db6b23b3b0230169a15e5f1bfe086539f4be43e767b2db68e8ee4
  languageName: node
  linkType: hard

"@kwsites/promise-deferred@npm:^1.1.1":
  version: 1.1.1
  resolution: "@kwsites/promise-deferred@npm:1.1.1"
  checksum: 10c0/ef1ad3f1f50991e3bed352b175986d8b4bc684521698514a2ed63c1d1fc9848843da4f2bc2df961c9b148c94e1c34bf33f0da8a90ba2234e452481f2cc9937b1
  languageName: node
  linkType: hard

"@mdx-js/loader@npm:2.3.0":
  version: 2.3.0
  resolution: "@mdx-js/loader@npm:2.3.0"
  dependencies:
    "@mdx-js/mdx": "npm:^2.0.0"
    source-map: "npm:^0.7.0"
  peerDependencies:
    webpack: ">=4"
  checksum: 10c0/64ff6c45a4ff7f344de57bc466958fbbc93b5c1dbde8af9f2b5e7b05c85fb9c71317ebf7b890371d19fc44ab7b89e67931479c22089cf9765f4afde08ffb9698
  languageName: node
  linkType: hard

"@mdx-js/mdx@npm:2.3.0, @mdx-js/mdx@npm:^2.0.0":
  version: 2.3.0
  resolution: "@mdx-js/mdx@npm:2.3.0"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/mdx": "npm:^2.0.0"
    estree-util-build-jsx: "npm:^2.0.0"
    estree-util-is-identifier-name: "npm:^2.0.0"
    estree-util-to-js: "npm:^1.1.0"
    estree-walker: "npm:^3.0.0"
    hast-util-to-estree: "npm:^2.0.0"
    markdown-extensions: "npm:^1.0.0"
    periscopic: "npm:^3.0.0"
    remark-mdx: "npm:^2.0.0"
    remark-parse: "npm:^10.0.0"
    remark-rehype: "npm:^10.0.0"
    unified: "npm:^10.0.0"
    unist-util-position-from-estree: "npm:^1.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
    unist-util-visit: "npm:^4.0.0"
    vfile: "npm:^5.0.0"
  checksum: 10c0/719384d8e72abd3e83aa2fd3010394636e32cc0e5e286b6414427ef03121397586ce97ec816afcc4d2b22ba65939c3801a8198e04cf921dd597c0aa9fd75dbb4
  languageName: node
  linkType: hard

"@mdx-js/react@npm:2.3.0":
  version: 2.3.0
  resolution: "@mdx-js/react@npm:2.3.0"
  dependencies:
    "@types/mdx": "npm:^2.0.0"
    "@types/react": "npm:>=16"
  peerDependencies:
    react: ">=16"
  checksum: 10c0/6d647115703dbe258f7fe372499fa8c6fe17a053ff0f2a208111c9973a71ae738a0ed376770445d39194d217e00e1a015644b24f32c2f7cb4f57988de0649b15
  languageName: node
  linkType: hard

"@mermaid-js/parser@npm:^0.4.0":
  version: 0.4.0
  resolution: "@mermaid-js/parser@npm:0.4.0"
  dependencies:
    langium: "npm:3.3.1"
  checksum: 10c0/f0bea89b993c89d9e655e487e6ffd6866897e607264e70a7addc4794683f5c9632376c1e9893246e7e2d5c05569d1b35005a213c283107453b8dff273fb8d8b2
  languageName: node
  linkType: hard

"@module-federation/error-codes@npm:0.8.4":
  version: 0.8.4
  resolution: "@module-federation/error-codes@npm:0.8.4"
  checksum: 10c0/970508e4edf0f443eec5c1a82aba342be5235d44dedf2f8d68739151878894afde8f04859fcebff688016e2a369a9f0273171378bc691d002901295afe142bf4
  languageName: node
  linkType: hard

"@module-federation/runtime-tools@npm:0.8.4":
  version: 0.8.4
  resolution: "@module-federation/runtime-tools@npm:0.8.4"
  dependencies:
    "@module-federation/runtime": "npm:0.8.4"
    "@module-federation/webpack-bundler-runtime": "npm:0.8.4"
  checksum: 10c0/82da5767a7384b898ce988c06434c26280aa0756e07a646ffee0d8da8e1ba2e65d5d55643c522f6d96c2e208b2a50bf57c0fe8769510d7711780f9c65f8ac18f
  languageName: node
  linkType: hard

"@module-federation/runtime@npm:0.8.4":
  version: 0.8.4
  resolution: "@module-federation/runtime@npm:0.8.4"
  dependencies:
    "@module-federation/error-codes": "npm:0.8.4"
    "@module-federation/sdk": "npm:0.8.4"
  checksum: 10c0/916ce10f6c12c2b319a2e20d07dc83baa2630fe591b6fba68453d91bb65b5bafec7370ad814cc4128be92ea9c857d880716e1341d3a74c0d66bcf2f1f7f16ece
  languageName: node
  linkType: hard

"@module-federation/sdk@npm:0.8.4":
  version: 0.8.4
  resolution: "@module-federation/sdk@npm:0.8.4"
  dependencies:
    isomorphic-rslog: "npm:0.0.6"
  checksum: 10c0/817fb03ae8136e648ce4f6f92419ed47bf1644c7adb744c91ddb7dabf9530e1a293c3012b61f537c401a728b4a2c9029065369e02cb3d7f26bb3b82a45e836be
  languageName: node
  linkType: hard

"@module-federation/webpack-bundler-runtime@npm:0.8.4":
  version: 0.8.4
  resolution: "@module-federation/webpack-bundler-runtime@npm:0.8.4"
  dependencies:
    "@module-federation/runtime": "npm:0.8.4"
    "@module-federation/sdk": "npm:0.8.4"
  checksum: 10c0/3192ef3e08486d17cad5f8a35dd34b6c0f56d4c40097487d1ee59f6a68cc52eb8923927b87f1c1afee27851739b08999cb310643a1bd761c7da936b85b241a7c
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@openapi-contrib/openapi-schema-to-json-schema@npm:^5.1.0":
  version: 5.1.0
  resolution: "@openapi-contrib/openapi-schema-to-json-schema@npm:5.1.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.12"
    "@types/lodash": "npm:^4.14.195"
    "@types/node": "npm:^20.4.1"
    fast-deep-equal: "npm:^3.1.3"
    lodash: "npm:^4.17.21"
    openapi-typescript: "npm:^5.4.1"
    yargs: "npm:^17.7.2"
  bin:
    openapi-schema-to-json-schema: dist/bin.js
  checksum: 10c0/a3a3c5bfe09214f38187ede604fcd76f5f48832f1c0a888819e450bb8aef881c24d2217f3b5bb49c6045b8a4e9f39228b8e8b874b6bffe415daa2eee34b8f34a
  languageName: node
  linkType: hard

"@pdf-lib/standard-fonts@npm:^1.0.0":
  version: 1.0.0
  resolution: "@pdf-lib/standard-fonts@npm:1.0.0"
  dependencies:
    pako: "npm:^1.0.6"
  checksum: 10c0/c683adfb764cd235a8370a0c1d5a8d7e90e3499ad33cdecfb92e4d48b0d36cfd038e3a875ebd0937a5646ee1578d793ab98f9c374be360c9a05d2699c1caedf4
  languageName: node
  linkType: hard

"@pdf-lib/upng@npm:^1.0.1":
  version: 1.0.1
  resolution: "@pdf-lib/upng@npm:1.0.1"
  dependencies:
    pako: "npm:^1.0.10"
  checksum: 10c0/9c300c513c1089e561c0cccac01f396a24efb9b0e9c922a39248cb09dfced70c05b9facdfce11a7f22cbedb4129593630a18111b90a57ef34ea4c3df98f2ac1d
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@playwright/browser-chromium@npm:^1.52.0":
  version: 1.52.0
  resolution: "@playwright/browser-chromium@npm:1.52.0"
  dependencies:
    playwright-core: "npm:1.52.0"
  checksum: 10c0/6cbe1d18550dcbc21279da7bcd65c2c8a94f68f4789ff21525ccb0c60a514ebe137a70843d5da043ba6210806ca845d2a3f52005b80a692801f9f230158a7af4
  languageName: node
  linkType: hard

"@remix-run/router@npm:1.22.0":
  version: 1.22.0
  resolution: "@remix-run/router@npm:1.22.0"
  checksum: 10c0/6fbfbdddb485af6bc24635272436fc9884b40d2517581b5cc66ab866279d238ccb11b6f8f67ad99d43ff21c0ea8bc088c96d510a42dcc0cc05a716760fe5a633
  languageName: node
  linkType: hard

"@rsbuild/core@npm:1.2.3":
  version: 1.2.3
  resolution: "@rsbuild/core@npm:1.2.3"
  dependencies:
    "@rspack/core": "npm:1.2.2"
    "@rspack/lite-tapable": "npm:~1.0.1"
    "@swc/helpers": "npm:^0.5.15"
    core-js: "npm:~3.40.0"
  bin:
    rsbuild: bin/rsbuild.js
  checksum: 10c0/12fdda36c76f4124495ccfe40a4bb14b64359a333cbf53ecc34bcc10702c3bfd025508beea99b63bcbe8054aa001c88379772615b94136fb1c535566233a848d
  languageName: node
  linkType: hard

"@rsbuild/plugin-less@npm:~1.1.0":
  version: 1.1.1
  resolution: "@rsbuild/plugin-less@npm:1.1.1"
  dependencies:
    deepmerge: "npm:^4.3.1"
    reduce-configs: "npm:^1.1.0"
  peerDependencies:
    "@rsbuild/core": 1.x
  checksum: 10c0/3bf54c60e0fd2e2e4cb384fd8b147cd86843f95c78584c88a919fc8b97df8bf34e62dd7362f6086c56e74ef664732c1c5aa313ae67358701d1d520b4dd067c13
  languageName: node
  linkType: hard

"@rsbuild/plugin-react@npm:~1.1.0":
  version: 1.1.1
  resolution: "@rsbuild/plugin-react@npm:1.1.1"
  dependencies:
    "@rspack/plugin-react-refresh": "npm:~1.0.1"
    react-refresh: "npm:^0.16.0"
  peerDependencies:
    "@rsbuild/core": 1.x
  checksum: 10c0/6aaa3d089f5e44aa93908769a281d3522065aa5104208d619ba9dc207e5843de33685b20d59d8ae9dad838207ae9dc93fe14d10187eaedbb94aefb417a1c4e4a
  languageName: node
  linkType: hard

"@rsbuild/plugin-sass@npm:~1.2.0":
  version: 1.2.2
  resolution: "@rsbuild/plugin-sass@npm:1.2.2"
  dependencies:
    deepmerge: "npm:^4.3.1"
    loader-utils: "npm:^2.0.4"
    postcss: "npm:^8.5.2"
    reduce-configs: "npm:^1.1.0"
    sass-embedded: "npm:^1.85.0"
  peerDependencies:
    "@rsbuild/core": 1.x
  checksum: 10c0/621dce077e1415bed27fb00eb35a205e62039a319a445fa7d6c191c817069c4c5085a53375ce638e87de48ddb0ba37f73a2ee2bc26087ff41aa0edf2f3fa795e
  languageName: node
  linkType: hard

"@rsbuild/plugin-yaml@npm:^1.0.2":
  version: 1.0.2
  resolution: "@rsbuild/plugin-yaml@npm:1.0.2"
  peerDependencies:
    "@rsbuild/core": 1.x || ^1.0.1-beta.0
  peerDependenciesMeta:
    "@rsbuild/core":
      optional: true
  checksum: 10c0/5062bd2501239729d4c2ea05cc3585361177a54f745329d81a75279fb1af4760de70de0cc9bc75b2b7764355fb961a0080b12a0adc47a1d300c0acb9e01337e5
  languageName: node
  linkType: hard

"@rspack/binding-darwin-arm64@npm:1.2.2":
  version: 1.2.2
  resolution: "@rspack/binding-darwin-arm64@npm:1.2.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rspack/binding-darwin-x64@npm:1.2.2":
  version: 1.2.2
  resolution: "@rspack/binding-darwin-x64@npm:1.2.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rspack/binding-linux-arm64-gnu@npm:1.2.2":
  version: 1.2.2
  resolution: "@rspack/binding-linux-arm64-gnu@npm:1.2.2"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rspack/binding-linux-arm64-musl@npm:1.2.2":
  version: 1.2.2
  resolution: "@rspack/binding-linux-arm64-musl@npm:1.2.2"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rspack/binding-linux-x64-gnu@npm:1.2.2":
  version: 1.2.2
  resolution: "@rspack/binding-linux-x64-gnu@npm:1.2.2"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rspack/binding-linux-x64-musl@npm:1.2.2":
  version: 1.2.2
  resolution: "@rspack/binding-linux-x64-musl@npm:1.2.2"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rspack/binding-win32-arm64-msvc@npm:1.2.2":
  version: 1.2.2
  resolution: "@rspack/binding-win32-arm64-msvc@npm:1.2.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rspack/binding-win32-ia32-msvc@npm:1.2.2":
  version: 1.2.2
  resolution: "@rspack/binding-win32-ia32-msvc@npm:1.2.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rspack/binding-win32-x64-msvc@npm:1.2.2":
  version: 1.2.2
  resolution: "@rspack/binding-win32-x64-msvc@npm:1.2.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@rspack/binding@npm:1.2.2":
  version: 1.2.2
  resolution: "@rspack/binding@npm:1.2.2"
  dependencies:
    "@rspack/binding-darwin-arm64": "npm:1.2.2"
    "@rspack/binding-darwin-x64": "npm:1.2.2"
    "@rspack/binding-linux-arm64-gnu": "npm:1.2.2"
    "@rspack/binding-linux-arm64-musl": "npm:1.2.2"
    "@rspack/binding-linux-x64-gnu": "npm:1.2.2"
    "@rspack/binding-linux-x64-musl": "npm:1.2.2"
    "@rspack/binding-win32-arm64-msvc": "npm:1.2.2"
    "@rspack/binding-win32-ia32-msvc": "npm:1.2.2"
    "@rspack/binding-win32-x64-msvc": "npm:1.2.2"
  dependenciesMeta:
    "@rspack/binding-darwin-arm64":
      optional: true
    "@rspack/binding-darwin-x64":
      optional: true
    "@rspack/binding-linux-arm64-gnu":
      optional: true
    "@rspack/binding-linux-arm64-musl":
      optional: true
    "@rspack/binding-linux-x64-gnu":
      optional: true
    "@rspack/binding-linux-x64-musl":
      optional: true
    "@rspack/binding-win32-arm64-msvc":
      optional: true
    "@rspack/binding-win32-ia32-msvc":
      optional: true
    "@rspack/binding-win32-x64-msvc":
      optional: true
  checksum: 10c0/4d9453847808675f0dbcb629b3acee4bda01ce91b4f3b2fbde9f26518535ef9a186bc5623461c2d46c9f311a46397296d8916c524acb5c40b2440867c8ee5ff7
  languageName: node
  linkType: hard

"@rspack/core@npm:1.2.2":
  version: 1.2.2
  resolution: "@rspack/core@npm:1.2.2"
  dependencies:
    "@module-federation/runtime-tools": "npm:0.8.4"
    "@rspack/binding": "npm:1.2.2"
    "@rspack/lite-tapable": "npm:1.0.1"
    caniuse-lite: "npm:^1.0.30001616"
  peerDependencies:
    "@rspack/tracing": ^1.x
    "@swc/helpers": ">=0.5.1"
  peerDependenciesMeta:
    "@rspack/tracing":
      optional: true
    "@swc/helpers":
      optional: true
  checksum: 10c0/299f0161d5532b784197d632bb12cf996b7504264997de0cf6d72e45ee50ffe29af392899dddfcb4eb48a44199262d1d318d7710a775ae62cba6a374e850ab24
  languageName: node
  linkType: hard

"@rspack/lite-tapable@npm:1.0.1, @rspack/lite-tapable@npm:~1.0.1":
  version: 1.0.1
  resolution: "@rspack/lite-tapable@npm:1.0.1"
  checksum: 10c0/90bb1bc414dc51ea2d0933e09f78d25584f3f50a85f4cb8228930bd29e5931bf55eff4f348a06c51dd3149fc73b8ae3920bf0ae5ae8a0c9fe1d9b404e6ecf5b7
  languageName: node
  linkType: hard

"@rspack/plugin-react-refresh@npm:~1.0.1":
  version: 1.0.3
  resolution: "@rspack/plugin-react-refresh@npm:1.0.3"
  dependencies:
    error-stack-parser: "npm:^2.1.4"
    html-entities: "npm:^2.5.2"
  peerDependencies:
    react-refresh: ">=0.10.0 <1.0.0"
    webpack-hot-middleware: 2.x
  peerDependenciesMeta:
    webpack-hot-middleware:
      optional: true
  checksum: 10c0/0bea598d7ec4698762b738958fcdabaf232c63dcd8ce990538bc7bc3e1e2d75b86c1704322202993317fa6fc01b1578315ddce1dda72306cbd3878710e61138f
  languageName: node
  linkType: hard

"@rspress/core@npm:1.43.12":
  version: 1.43.12
  resolution: "@rspress/core@npm:1.43.12"
  dependencies:
    "@mdx-js/loader": "npm:2.3.0"
    "@mdx-js/mdx": "npm:2.3.0"
    "@mdx-js/react": "npm:2.3.0"
    "@rsbuild/core": "npm:1.2.3"
    "@rsbuild/plugin-less": "npm:~1.1.0"
    "@rsbuild/plugin-react": "npm:~1.1.0"
    "@rsbuild/plugin-sass": "npm:~1.2.0"
    "@rspress/mdx-rs": "npm:0.6.6"
    "@rspress/plugin-auto-nav-sidebar": "npm:1.43.12"
    "@rspress/plugin-container-syntax": "npm:1.43.12"
    "@rspress/plugin-last-updated": "npm:1.43.12"
    "@rspress/plugin-medium-zoom": "npm:1.43.12"
    "@rspress/runtime": "npm:1.43.12"
    "@rspress/shared": "npm:1.43.12"
    "@rspress/theme-default": "npm:1.43.12"
    enhanced-resolve: "npm:5.18.0"
    github-slugger: "npm:^2.0.0"
    hast-util-from-html: "npm:^2.0.3"
    hast-util-heading-rank: "npm:^2.1.1"
    html-to-text: "npm:^9.0.5"
    htmr: "npm:^1.0.2"
    lodash-es: "npm:^4.17.21"
    mdast-util-mdxjs-esm: "npm:^1.3.1"
    memfs: "npm:^4.17.0"
    picocolors: "npm:^1.1.1"
    react: "npm:^18.3.1"
    react-dom: "npm:^18.3.1"
    react-helmet-async: "npm:^1.3.0"
    react-lazy-with-preload: "npm:^2.2.1"
    react-syntax-highlighter: "npm:^15.6.1"
    rehype-external-links: "npm:^3.0.0"
    remark: "npm:^14.0.3"
    remark-gfm: "npm:3.0.1"
    rspack-plugin-virtual-module: "npm:0.1.13"
    tinyglobby: "npm:^0.2.10"
    unified: "npm:^10.1.2"
    unist-util-visit: "npm:^4.1.2"
    unist-util-visit-children: "npm:^2.0.2"
  checksum: 10c0/a0729278213db4ceb18ac89fdc1031936733f3fcaf9cf38985f67bb68e68f4d45a46ef2da691a82f70889e82a8428b84bcbd15eb0d3dbd0f29846d36c10744a6
  languageName: node
  linkType: hard

"@rspress/mdx-rs-darwin-arm64@npm:0.6.6":
  version: 0.6.6
  resolution: "@rspress/mdx-rs-darwin-arm64@npm:0.6.6"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rspress/mdx-rs-darwin-x64@npm:0.6.6":
  version: 0.6.6
  resolution: "@rspress/mdx-rs-darwin-x64@npm:0.6.6"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rspress/mdx-rs-linux-arm64-gnu@npm:0.6.6":
  version: 0.6.6
  resolution: "@rspress/mdx-rs-linux-arm64-gnu@npm:0.6.6"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@rspress/mdx-rs-linux-arm64-musl@npm:0.6.6":
  version: 0.6.6
  resolution: "@rspress/mdx-rs-linux-arm64-musl@npm:0.6.6"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@rspress/mdx-rs-linux-x64-gnu@npm:0.6.6":
  version: 0.6.6
  resolution: "@rspress/mdx-rs-linux-x64-gnu@npm:0.6.6"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@rspress/mdx-rs-linux-x64-musl@npm:0.6.6":
  version: 0.6.6
  resolution: "@rspress/mdx-rs-linux-x64-musl@npm:0.6.6"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@rspress/mdx-rs-win32-arm64-msvc@npm:0.6.6":
  version: 0.6.6
  resolution: "@rspress/mdx-rs-win32-arm64-msvc@npm:0.6.6"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rspress/mdx-rs-win32-x64-msvc@npm:0.6.6":
  version: 0.6.6
  resolution: "@rspress/mdx-rs-win32-x64-msvc@npm:0.6.6"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@rspress/mdx-rs@npm:0.6.6":
  version: 0.6.6
  resolution: "@rspress/mdx-rs@npm:0.6.6"
  dependencies:
    "@rspress/mdx-rs-darwin-arm64": "npm:0.6.6"
    "@rspress/mdx-rs-darwin-x64": "npm:0.6.6"
    "@rspress/mdx-rs-linux-arm64-gnu": "npm:0.6.6"
    "@rspress/mdx-rs-linux-arm64-musl": "npm:0.6.6"
    "@rspress/mdx-rs-linux-x64-gnu": "npm:0.6.6"
    "@rspress/mdx-rs-linux-x64-musl": "npm:0.6.6"
    "@rspress/mdx-rs-win32-arm64-msvc": "npm:0.6.6"
    "@rspress/mdx-rs-win32-x64-msvc": "npm:0.6.6"
  dependenciesMeta:
    "@rspress/mdx-rs-darwin-arm64":
      optional: true
    "@rspress/mdx-rs-darwin-x64":
      optional: true
    "@rspress/mdx-rs-linux-arm64-gnu":
      optional: true
    "@rspress/mdx-rs-linux-arm64-musl":
      optional: true
    "@rspress/mdx-rs-linux-x64-gnu":
      optional: true
    "@rspress/mdx-rs-linux-x64-musl":
      optional: true
    "@rspress/mdx-rs-win32-arm64-msvc":
      optional: true
    "@rspress/mdx-rs-win32-x64-msvc":
      optional: true
  checksum: 10c0/a8757bec983b2d6c0952a56ea9defd013323f74c306de10213e52082d3031524b87c0ef1c4661f9d741932de609855b59bf2c9f7c162b92d5d507180df305e83
  languageName: node
  linkType: hard

"@rspress/plugin-auto-nav-sidebar@npm:1.43.12":
  version: 1.43.12
  resolution: "@rspress/plugin-auto-nav-sidebar@npm:1.43.12"
  dependencies:
    "@rspress/shared": "npm:1.43.12"
  checksum: 10c0/da8a4d77c14a4b640b2529eba03ba2f65595c4530a4b4f7b5f88612c3467e617fa6d731cd20303dde7f815c41fea9eb88dee020519d0c7f99bfb255eab81a392
  languageName: node
  linkType: hard

"@rspress/plugin-container-syntax@npm:1.43.12":
  version: 1.43.12
  resolution: "@rspress/plugin-container-syntax@npm:1.43.12"
  dependencies:
    "@rspress/shared": "npm:1.43.12"
  checksum: 10c0/8f93b78514db1a4161529e31563906fff3cae9ea5ca346f61da219eabcda99cc4c0741fa271d27e8d5d6f6c762698dceaf3653389ceb891df7533addab5f9211
  languageName: node
  linkType: hard

"@rspress/plugin-last-updated@npm:1.43.12":
  version: 1.43.12
  resolution: "@rspress/plugin-last-updated@npm:1.43.12"
  dependencies:
    "@rspress/shared": "npm:1.43.12"
  checksum: 10c0/d45dfcccac9f8a632b75ac06dbd016672a936dc0f239775dedbb023ca7a016f5f73288691f17d8300a206b5866682f0324b2043f54590dc9e911847e4936ab2c
  languageName: node
  linkType: hard

"@rspress/plugin-medium-zoom@npm:1.43.12":
  version: 1.43.12
  resolution: "@rspress/plugin-medium-zoom@npm:1.43.12"
  dependencies:
    medium-zoom: "npm:1.1.0"
  peerDependencies:
    "@rspress/runtime": ^1.43.12
  checksum: 10c0/6902c6553d8022a175d74485d5105e2d1c809ea86efa84ce2744ea5a3a69920294bf24bb8fab922926b6ff13d21d6be93cfd64108dfd9ad377c3849d9242e979
  languageName: node
  linkType: hard

"@rspress/runtime@npm:1.43.12":
  version: 1.43.12
  resolution: "@rspress/runtime@npm:1.43.12"
  dependencies:
    "@rspress/shared": "npm:1.43.12"
    react: "npm:^18.3.1"
    react-dom: "npm:^18.3.1"
    react-helmet-async: "npm:^1.3.0"
    react-router-dom: "npm:^6.29.0"
  checksum: 10c0/a21c4e37a5279fb506ebaa6cf574341f8ffee52b828cd4a6d6fdf998dfc00e4381d2189f72f7e175e00890d1dedef71b42b9d0a6752e3223a22e3170bdff0721
  languageName: node
  linkType: hard

"@rspress/shared@npm:1.43.12":
  version: 1.43.12
  resolution: "@rspress/shared@npm:1.43.12"
  dependencies:
    "@rsbuild/core": "npm:1.2.3"
    gray-matter: "npm:4.0.3"
    lodash-es: "npm:^4.17.21"
    unified: "npm:^10.1.2"
  checksum: 10c0/3d62c1e4ad0643977100409a4c1fd8d2bbef40771841d88a32a9dcfae1b8717c7910c373bd054ced08de50aaa601b1a6a205ef325e3c5487fa16572d7645df9b
  languageName: node
  linkType: hard

"@rspress/theme-default@npm:1.43.12":
  version: 1.43.12
  resolution: "@rspress/theme-default@npm:1.43.12"
  dependencies:
    "@mdx-js/react": "npm:2.3.0"
    "@rspress/runtime": "npm:1.43.12"
    "@rspress/shared": "npm:1.43.12"
    body-scroll-lock: "npm:4.0.0-beta.0"
    copy-to-clipboard: "npm:^3.3.3"
    flexsearch: "npm:0.7.43"
    github-slugger: "npm:^2.0.0"
    htmr: "npm:^1.0.2"
    lodash-es: "npm:^4.17.21"
    nprogress: "npm:^0.2.0"
    react: "npm:^18.3.1"
    react-dom: "npm:^18.3.1"
    react-helmet-async: "npm:^1.3.0"
    react-syntax-highlighter: "npm:^15.6.1"
  checksum: 10c0/3f60eabcaa30933ce694618c3f9d08a70e121c10e6ce23c7bdc422c5d8584f54459f7600628ba2b428ab56b8785102ebc6b22f4f3dc807265ba9a8dd912a6a5a
  languageName: node
  linkType: hard

"@selderee/plugin-htmlparser2@npm:^0.11.0":
  version: 0.11.0
  resolution: "@selderee/plugin-htmlparser2@npm:0.11.0"
  dependencies:
    domhandler: "npm:^5.0.3"
    selderee: "npm:^0.11.0"
  checksum: 10c0/e938ba9aeb31a9cf30dcb2977ef41685c598bf744bedc88c57aa9e8b7e71b51781695cf99c08aac50773fd7714eba670bd2a079e46db0788abe40c6d220084eb
  languageName: node
  linkType: hard

"@shikijs/core@npm:3.4.0":
  version: 3.4.0
  resolution: "@shikijs/core@npm:3.4.0"
  dependencies:
    "@shikijs/types": "npm:3.4.0"
    "@shikijs/vscode-textmate": "npm:^10.0.2"
    "@types/hast": "npm:^3.0.4"
    hast-util-to-html: "npm:^9.0.5"
  checksum: 10c0/715d1e994dcbcbcd76bbe64d0f7f703a561b01e3d8d17392352039ec65a621ce7fd9866833ec9bdb4dd2fad8c2415fa92c055ac61bcc3f64cae87b646e8405b2
  languageName: node
  linkType: hard

"@shikijs/engine-javascript@npm:3.4.0":
  version: 3.4.0
  resolution: "@shikijs/engine-javascript@npm:3.4.0"
  dependencies:
    "@shikijs/types": "npm:3.4.0"
    "@shikijs/vscode-textmate": "npm:^10.0.2"
    oniguruma-to-es: "npm:^4.3.3"
  checksum: 10c0/98abc6990c63ded2602b7c70e1d9761937ed174b6a639057dee8a6b96e45364ec823827e8563140f5b74b94e6aef8bb657d06146f51d8253ced8776ad9d896f6
  languageName: node
  linkType: hard

"@shikijs/engine-oniguruma@npm:3.4.0":
  version: 3.4.0
  resolution: "@shikijs/engine-oniguruma@npm:3.4.0"
  dependencies:
    "@shikijs/types": "npm:3.4.0"
    "@shikijs/vscode-textmate": "npm:^10.0.2"
  checksum: 10c0/7a8b6c5ad27e28e7483df5cb939963571bb7df90a77b571c654d3de0ae3678d37d6b0bf5f77ecdf9ecbc6a2dbccc32719f96ced0d303561c4d945c7ac642bf29
  languageName: node
  linkType: hard

"@shikijs/langs@npm:3.4.0":
  version: 3.4.0
  resolution: "@shikijs/langs@npm:3.4.0"
  dependencies:
    "@shikijs/types": "npm:3.4.0"
  checksum: 10c0/73f464e7e433b42fa9c25ebc4bcca1832f861ee9342ccc63090328f3516af6d4fc685cae84e330b9a4aee69f095922403b2b345d0412e7dc7b0be66a1e3bbf68
  languageName: node
  linkType: hard

"@shikijs/themes@npm:3.4.0":
  version: 3.4.0
  resolution: "@shikijs/themes@npm:3.4.0"
  dependencies:
    "@shikijs/types": "npm:3.4.0"
  checksum: 10c0/adca4a5b2ce2d663b39caed2f9002cda5cab96c938cd594e2924e78e9a360a8cb886d3e87ce2773b5c1f4ef94f96281667b41dcb55dbd7e655ef1edcf775c6cd
  languageName: node
  linkType: hard

"@shikijs/transformers@npm:^3.4.0":
  version: 3.4.0
  resolution: "@shikijs/transformers@npm:3.4.0"
  dependencies:
    "@shikijs/core": "npm:3.4.0"
    "@shikijs/types": "npm:3.4.0"
  checksum: 10c0/ada3e08f8617b2a1a2da7494ef42353f0095fc9294cf08e732a78a8b5f64f5e6f6dd7112d90cdca3325e567c8b09c3f300569daef8ac96dd06962245995e8304
  languageName: node
  linkType: hard

"@shikijs/types@npm:3.4.0":
  version: 3.4.0
  resolution: "@shikijs/types@npm:3.4.0"
  dependencies:
    "@shikijs/vscode-textmate": "npm:^10.0.2"
    "@types/hast": "npm:^3.0.4"
  checksum: 10c0/3a2d51208981e1ca70101dc5575e60cffca223cdc0aeb1683b3b4a582205edf185c104b41c5092b2dfca96d1f81a88d12e16161cb8288287949b90ac5eabf531
  languageName: node
  linkType: hard

"@shikijs/vscode-textmate@npm:^10.0.2":
  version: 10.0.2
  resolution: "@shikijs/vscode-textmate@npm:10.0.2"
  checksum: 10c0/36b682d691088ec244de292dc8f91b808f95c89466af421cf84cbab92230f03c8348649c14b3251991b10ce632b0c715e416e992dd5f28ff3221dc2693fd9462
  languageName: node
  linkType: hard

"@swc/helpers@npm:^0.5.15":
  version: 0.5.15
  resolution: "@swc/helpers@npm:0.5.15"
  dependencies:
    tslib: "npm:^2.8.0"
  checksum: 10c0/33002f74f6f885f04c132960835fdfc474186983ea567606db62e86acd0680ca82f34647e8e610f4e1e422d1c16fce729dde22cd3b797ab1fd9061a825dabca4
  languageName: node
  linkType: hard

"@types/acorn@npm:^4.0.0":
  version: 4.0.6
  resolution: "@types/acorn@npm:4.0.6"
  dependencies:
    "@types/estree": "npm:*"
  checksum: 10c0/5a65a1d7e91fc95703f0a717897be60fa7ccd34b17f5462056274a246e6690259fe0a1baabc86fd3260354f87245cb3dc483346d7faad2b78fc199763978ede9
  languageName: node
  linkType: hard

"@types/d3-array@npm:*":
  version: 3.2.1
  resolution: "@types/d3-array@npm:3.2.1"
  checksum: 10c0/38bf2c778451f4b79ec81a2288cb4312fe3d6449ecdf562970cc339b60f280f31c93a024c7ff512607795e79d3beb0cbda123bb07010167bce32927f71364bca
  languageName: node
  linkType: hard

"@types/d3-axis@npm:*":
  version: 3.0.6
  resolution: "@types/d3-axis@npm:3.0.6"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10c0/d756d42360261f44d8eefd0950c5bb0a4f67a46dd92069da3f723ac36a1e8cb2b9ce6347d836ef19d5b8aef725dbcf8fdbbd6cfbff676ca4b0642df2f78b599a
  languageName: node
  linkType: hard

"@types/d3-brush@npm:*":
  version: 3.0.6
  resolution: "@types/d3-brush@npm:3.0.6"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10c0/fd6e2ac7657a354f269f6b9c58451ffae9d01b89ccb1eb6367fd36d635d2f1990967215ab498e0c0679ff269429c57fad6a2958b68f4d45bc9f81d81672edc01
  languageName: node
  linkType: hard

"@types/d3-chord@npm:*":
  version: 3.0.6
  resolution: "@types/d3-chord@npm:3.0.6"
  checksum: 10c0/c5a25eb5389db01e63faec0c5c2ec7cc41c494e9b3201630b494c4e862a60f1aa83fabbc33a829e7e1403941e3c30d206c741559b14406ac2a4239cfdf4b4c17
  languageName: node
  linkType: hard

"@types/d3-color@npm:*":
  version: 3.1.3
  resolution: "@types/d3-color@npm:3.1.3"
  checksum: 10c0/65eb0487de606eb5ad81735a9a5b3142d30bc5ea801ed9b14b77cb14c9b909f718c059f13af341264ee189acf171508053342142bdf99338667cea26a2d8d6ae
  languageName: node
  linkType: hard

"@types/d3-contour@npm:*":
  version: 3.0.6
  resolution: "@types/d3-contour@npm:3.0.6"
  dependencies:
    "@types/d3-array": "npm:*"
    "@types/geojson": "npm:*"
  checksum: 10c0/e7d83e94719af4576ceb5ac7f277c5806f83ba6c3631744ae391cffc3641f09dfa279470b83053cd0b2acd6784e8749c71141d05bdffa63ca58ffb5b31a0f27c
  languageName: node
  linkType: hard

"@types/d3-delaunay@npm:*":
  version: 6.0.4
  resolution: "@types/d3-delaunay@npm:6.0.4"
  checksum: 10c0/d154a8864f08c4ea23ecb9bdabcef1c406a25baa8895f0cb08a0ed2799de0d360e597552532ce7086ff0cdffa8f3563f9109d18f0191459d32bb620a36939123
  languageName: node
  linkType: hard

"@types/d3-dispatch@npm:*":
  version: 3.0.6
  resolution: "@types/d3-dispatch@npm:3.0.6"
  checksum: 10c0/405eb7d0ec139fbf72fa6a43b0f3ca8a1f913bb2cb38f607827e63fca8d4393f021f32f3b96b33c93ddbd37789453a0b3624f14f504add5308fd9aec8a46dda0
  languageName: node
  linkType: hard

"@types/d3-drag@npm:*":
  version: 3.0.7
  resolution: "@types/d3-drag@npm:3.0.7"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10c0/65e29fa32a87c72d26c44b5e2df3bf15af21cd128386bcc05bcacca255927c0397d0cd7e6062aed5f0abd623490544a9d061c195f5ed9f018fe0b698d99c079d
  languageName: node
  linkType: hard

"@types/d3-dsv@npm:*":
  version: 3.0.7
  resolution: "@types/d3-dsv@npm:3.0.7"
  checksum: 10c0/c0f01da862465594c8a28278b51c850af3b4239cc22b14fd1a19d7a98f93d94efa477bf59d8071beb285dca45bf614630811451e18e7c52add3a0abfee0a1871
  languageName: node
  linkType: hard

"@types/d3-ease@npm:*":
  version: 3.0.2
  resolution: "@types/d3-ease@npm:3.0.2"
  checksum: 10c0/aff5a1e572a937ee9bff6465225d7ba27d5e0c976bd9eacdac2e6f10700a7cb0c9ea2597aff6b43a6ed850a3210030870238894a77ec73e309b4a9d0333f099c
  languageName: node
  linkType: hard

"@types/d3-fetch@npm:*":
  version: 3.0.7
  resolution: "@types/d3-fetch@npm:3.0.7"
  dependencies:
    "@types/d3-dsv": "npm:*"
  checksum: 10c0/3d147efa52a26da1a5d40d4d73e6cebaaa964463c378068062999b93ea3731b27cc429104c21ecbba98c6090e58ef13429db6399238c5e3500162fb3015697a0
  languageName: node
  linkType: hard

"@types/d3-force@npm:*":
  version: 3.0.10
  resolution: "@types/d3-force@npm:3.0.10"
  checksum: 10c0/c82b459079a106b50e346c9b79b141f599f2fc4f598985a5211e72c7a2e20d35bd5dc6e91f306b323c8bfa325c02c629b1645f5243f1c6a55bd51bc85cccfa92
  languageName: node
  linkType: hard

"@types/d3-format@npm:*":
  version: 3.0.4
  resolution: "@types/d3-format@npm:3.0.4"
  checksum: 10c0/3ac1600bf9061a59a228998f7cd3f29e85cbf522997671ba18d4d84d10a2a1aff4f95aceb143fa9960501c3ec351e113fc75884e6a504ace44dc1744083035ee
  languageName: node
  linkType: hard

"@types/d3-geo@npm:*":
  version: 3.1.0
  resolution: "@types/d3-geo@npm:3.1.0"
  dependencies:
    "@types/geojson": "npm:*"
  checksum: 10c0/3745a93439038bb5b0b38facf435f7079812921d46406f5d38deaee59e90084ff742443c7ea0a8446df81a0d81eaf622fe7068cf4117a544bd4aa3b2dc182f88
  languageName: node
  linkType: hard

"@types/d3-hierarchy@npm:*":
  version: 3.1.7
  resolution: "@types/d3-hierarchy@npm:3.1.7"
  checksum: 10c0/873711737d6b8e7b6f1dda0bcd21294a48f75024909ae510c5d2c21fad2e72032e0958def4d9f68319d3aaac298ad09c49807f8bfc87a145a82693b5208613c7
  languageName: node
  linkType: hard

"@types/d3-interpolate@npm:*":
  version: 3.0.4
  resolution: "@types/d3-interpolate@npm:3.0.4"
  dependencies:
    "@types/d3-color": "npm:*"
  checksum: 10c0/066ebb8da570b518dd332df6b12ae3b1eaa0a7f4f0c702e3c57f812cf529cc3500ec2aac8dc094f31897790346c6b1ebd8cd7a077176727f4860c2b181a65ca4
  languageName: node
  linkType: hard

"@types/d3-path@npm:*":
  version: 3.1.1
  resolution: "@types/d3-path@npm:3.1.1"
  checksum: 10c0/2c36eb31ebaf2ce4712e793fd88087117976f7c4ed69cc2431825f999c8c77cca5cea286f3326432b770739ac6ccd5d04d851eb65e7a4dbcc10c982b49ad2c02
  languageName: node
  linkType: hard

"@types/d3-polygon@npm:*":
  version: 3.0.2
  resolution: "@types/d3-polygon@npm:3.0.2"
  checksum: 10c0/f46307bb32b6c2aef8c7624500e0f9b518de8f227ccc10170b869dc43e4c542560f6c8d62e9f087fac45e198d6e4b623e579c0422e34c85baf56717456d3f439
  languageName: node
  linkType: hard

"@types/d3-quadtree@npm:*":
  version: 3.0.6
  resolution: "@types/d3-quadtree@npm:3.0.6"
  checksum: 10c0/7eaa0a4d404adc856971c9285e1c4ab17e9135ea669d847d6db7e0066126a28ac751864e7ce99c65d526e130f56754a2e437a1617877098b3bdcc3ef23a23616
  languageName: node
  linkType: hard

"@types/d3-random@npm:*":
  version: 3.0.3
  resolution: "@types/d3-random@npm:3.0.3"
  checksum: 10c0/5f4fea40080cd6d4adfee05183d00374e73a10c530276a6455348983dda341003a251def28565a27c25d9cf5296a33e870e397c9d91ff83fb7495a21c96b6882
  languageName: node
  linkType: hard

"@types/d3-scale-chromatic@npm:*":
  version: 3.1.0
  resolution: "@types/d3-scale-chromatic@npm:3.1.0"
  checksum: 10c0/93c564e02d2e97a048e18fe8054e4a935335da6ab75a56c3df197beaa87e69122eef0dfbeb7794d4a444a00e52e3123514ee27cec084bd21f6425b7037828cc2
  languageName: node
  linkType: hard

"@types/d3-scale@npm:*":
  version: 4.0.9
  resolution: "@types/d3-scale@npm:4.0.9"
  dependencies:
    "@types/d3-time": "npm:*"
  checksum: 10c0/4ac44233c05cd50b65b33ecb35d99fdf07566bcdbc55bc1306b2f27d1c5134d8c560d356f2c8e76b096e9125ffb8d26d95f78d56e210d1c542cb255bdf31d6c8
  languageName: node
  linkType: hard

"@types/d3-selection@npm:*":
  version: 3.0.11
  resolution: "@types/d3-selection@npm:3.0.11"
  checksum: 10c0/0c512956c7503ff5def4bb32e0c568cc757b9a2cc400a104fc0f4cfe5e56d83ebde2a97821b6f2cb26a7148079d3b86a2f28e11d68324ed311cf35c2ed980d1d
  languageName: node
  linkType: hard

"@types/d3-shape@npm:*":
  version: 3.1.7
  resolution: "@types/d3-shape@npm:3.1.7"
  dependencies:
    "@types/d3-path": "npm:*"
  checksum: 10c0/38e59771c1c4c83b67aa1f941ce350410522a149d2175832fdc06396b2bb3b2c1a2dd549e0f8230f9f24296ee5641a515eaf10f55ee1ef6c4f83749e2dd7dcfd
  languageName: node
  linkType: hard

"@types/d3-time-format@npm:*":
  version: 4.0.3
  resolution: "@types/d3-time-format@npm:4.0.3"
  checksum: 10c0/9ef5e8e2b96b94799b821eed5d61a3d432c7903247966d8ad951b8ce5797fe46554b425cb7888fa5bf604b4663c369d7628c0328ffe80892156671c58d1a7f90
  languageName: node
  linkType: hard

"@types/d3-time@npm:*":
  version: 3.0.4
  resolution: "@types/d3-time@npm:3.0.4"
  checksum: 10c0/6d9e2255d63f7a313a543113920c612e957d70da4fb0890931da6c2459010291b8b1f95e149a538500c1c99e7e6c89ffcce5554dd29a31ff134a38ea94b6d174
  languageName: node
  linkType: hard

"@types/d3-timer@npm:*":
  version: 3.0.2
  resolution: "@types/d3-timer@npm:3.0.2"
  checksum: 10c0/c644dd9571fcc62b1aa12c03bcad40571553020feeb5811f1d8a937ac1e65b8a04b759b4873aef610e28b8714ac71c9885a4d6c127a048d95118f7e5b506d9e1
  languageName: node
  linkType: hard

"@types/d3-transition@npm:*":
  version: 3.0.9
  resolution: "@types/d3-transition@npm:3.0.9"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10c0/4f68b9df7ac745b3491216c54203cbbfa0f117ae4c60e2609cdef2db963582152035407fdff995b10ee383bae2f05b7743493f48e1b8e46df54faa836a8fb7b5
  languageName: node
  linkType: hard

"@types/d3-zoom@npm:*":
  version: 3.0.8
  resolution: "@types/d3-zoom@npm:3.0.8"
  dependencies:
    "@types/d3-interpolate": "npm:*"
    "@types/d3-selection": "npm:*"
  checksum: 10c0/1dbdbcafddcae12efb5beb6948546963f29599e18bc7f2a91fb69cc617c2299a65354f2d47e282dfb86fec0968406cd4fb7f76ba2d2fb67baa8e8d146eb4a547
  languageName: node
  linkType: hard

"@types/d3@npm:^7.4.3":
  version: 7.4.3
  resolution: "@types/d3@npm:7.4.3"
  dependencies:
    "@types/d3-array": "npm:*"
    "@types/d3-axis": "npm:*"
    "@types/d3-brush": "npm:*"
    "@types/d3-chord": "npm:*"
    "@types/d3-color": "npm:*"
    "@types/d3-contour": "npm:*"
    "@types/d3-delaunay": "npm:*"
    "@types/d3-dispatch": "npm:*"
    "@types/d3-drag": "npm:*"
    "@types/d3-dsv": "npm:*"
    "@types/d3-ease": "npm:*"
    "@types/d3-fetch": "npm:*"
    "@types/d3-force": "npm:*"
    "@types/d3-format": "npm:*"
    "@types/d3-geo": "npm:*"
    "@types/d3-hierarchy": "npm:*"
    "@types/d3-interpolate": "npm:*"
    "@types/d3-path": "npm:*"
    "@types/d3-polygon": "npm:*"
    "@types/d3-quadtree": "npm:*"
    "@types/d3-random": "npm:*"
    "@types/d3-scale": "npm:*"
    "@types/d3-scale-chromatic": "npm:*"
    "@types/d3-selection": "npm:*"
    "@types/d3-shape": "npm:*"
    "@types/d3-time": "npm:*"
    "@types/d3-time-format": "npm:*"
    "@types/d3-timer": "npm:*"
    "@types/d3-transition": "npm:*"
    "@types/d3-zoom": "npm:*"
  checksum: 10c0/a9c6d65b13ef3b42c87f2a89ea63a6d5640221869f97d0657b0cb2f1dac96a0f164bf5605643c0794e0de3aa2bf05df198519aaf15d24ca135eb0e8bd8a9d879
  languageName: node
  linkType: hard

"@types/debug@npm:^4.0.0":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "npm:*"
  checksum: 10c0/5dcd465edbb5a7f226e9a5efd1f399c6172407ef5840686b73e3608ce135eeca54ae8037dcd9f16bdb2768ac74925b820a8b9ecc588a58ca09eca6acabe33e2f
  languageName: node
  linkType: hard

"@types/estree-jsx@npm:^1.0.0":
  version: 1.0.5
  resolution: "@types/estree-jsx@npm:1.0.5"
  dependencies:
    "@types/estree": "npm:*"
  checksum: 10c0/07b354331516428b27a3ab99ee397547d47eb223c34053b48f84872fafb841770834b90cc1a0068398e7c7ccb15ec51ab00ec64b31dc5e3dbefd624638a35c6d
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.0, @types/estree@npm:^1.0.6":
  version: 1.0.6
  resolution: "@types/estree@npm:1.0.6"
  checksum: 10c0/cdfd751f6f9065442cd40957c07fd80361c962869aa853c1c2fd03e101af8b9389d8ff4955a43a6fcfa223dd387a089937f95be0f3eec21ca527039fd2d9859a
  languageName: node
  linkType: hard

"@types/geojson@npm:*":
  version: 7946.0.16
  resolution: "@types/geojson@npm:7946.0.16"
  checksum: 10c0/1ff24a288bd5860b766b073ead337d31d73bdc715e5b50a2cee5cb0af57a1ed02cc04ef295f5fa68dc40fe3e4f104dd31282b2b818a5ba3231bc1001ba084e3c
  languageName: node
  linkType: hard

"@types/hast@npm:^2.0.0":
  version: 2.3.10
  resolution: "@types/hast@npm:2.3.10"
  dependencies:
    "@types/unist": "npm:^2"
  checksum: 10c0/16daac35d032e656defe1f103f9c09c341a6dc553c7ec17b388274076fa26e904a71ea5ea41fd368a6d5f1e9e53be275c80af7942b9c466d8511d261c9529c7e
  languageName: node
  linkType: hard

"@types/hast@npm:^3.0.0, @types/hast@npm:^3.0.4":
  version: 3.0.4
  resolution: "@types/hast@npm:3.0.4"
  dependencies:
    "@types/unist": "npm:*"
  checksum: 10c0/3249781a511b38f1d330fd1e3344eed3c4e7ea8eff82e835d35da78e637480d36fad37a78be5a7aed8465d237ad0446abc1150859d0fde395354ea634decf9f7
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.12":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/lodash@npm:^4.14.195":
  version: 4.17.15
  resolution: "@types/lodash@npm:4.17.15"
  checksum: 10c0/2eb2dc6d231f5fb4603d176c08c8d7af688f574d09af47466a179cd7812d9f64144ba74bb32ca014570ffdc544eedc51b7a5657212bad083b6eecbd72223f9bb
  languageName: node
  linkType: hard

"@types/mdast@npm:^3.0.0":
  version: 3.0.15
  resolution: "@types/mdast@npm:3.0.15"
  dependencies:
    "@types/unist": "npm:^2"
  checksum: 10c0/fcbf716c03d1ed5465deca60862e9691414f9c43597c288c7d2aefbe274552e1bbd7aeee91b88a02597e88a28c139c57863d0126fcf8416a95fdc681d054ee3d
  languageName: node
  linkType: hard

"@types/mdast@npm:^4.0.0":
  version: 4.0.4
  resolution: "@types/mdast@npm:4.0.4"
  dependencies:
    "@types/unist": "npm:*"
  checksum: 10c0/84f403dbe582ee508fd9c7643ac781ad8597fcbfc9ccb8d4715a2c92e4545e5772cbd0dbdf18eda65789386d81b009967fdef01b24faf6640f817287f54d9c82
  languageName: node
  linkType: hard

"@types/mdx@npm:^2.0.0":
  version: 2.0.13
  resolution: "@types/mdx@npm:2.0.13"
  checksum: 10c0/5edf1099505ac568da55f9ae8a93e7e314e8cbc13d3445d0be61b75941226b005e1390d9b95caecf5dcb00c9d1bab2f1f60f6ff9876dc091a48b547495007720
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 2.1.0
  resolution: "@types/ms@npm:2.1.0"
  checksum: 10c0/5ce692ffe1549e1b827d99ef8ff71187457e0eb44adbae38fdf7b9a74bae8d20642ee963c14516db1d35fa2652e65f47680fdf679dcbde52bbfadd021f497225
  languageName: node
  linkType: hard

"@types/node@npm:^20.4.1":
  version: 20.17.19
  resolution: "@types/node@npm:20.17.19"
  dependencies:
    undici-types: "npm:~6.19.2"
  checksum: 10c0/930e554eadeb0c2848f9225007ef66ea9c7fe987c80e2f2c1a9f316b297036d4054bbc5459b67051cf32cd360a022344c2998dcb1ec7579109f631b6d5f21bf8
  languageName: node
  linkType: hard

"@types/parse5@npm:^6.0.0":
  version: 6.0.3
  resolution: "@types/parse5@npm:6.0.3"
  checksum: 10c0/a7c7ef6625974b74b93c1105953003a2291897e453369efcadc569b907de2784d61d4e6905de3ef959fa07f3278f41ed0c22ead0173776023fc43b6ed31042d0
  languageName: node
  linkType: hard

"@types/prop-types@npm:^15.0.0":
  version: 15.7.14
  resolution: "@types/prop-types@npm:15.7.14"
  checksum: 10c0/1ec775160bfab90b67a782d735952158c7e702ca4502968aa82565bd8e452c2de8601c8dfe349733073c31179116cf7340710160d3836aa8a1ef76d1532893b1
  languageName: node
  linkType: hard

"@types/react@npm:>=16":
  version: 19.0.10
  resolution: "@types/react@npm:19.0.10"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10c0/41884cca21850c8b2d6578b172ca0ca4fff6021251a68532b19f2031ac23dc5a9222470208065f8d9985d367376047df2f49ece8d927f7d04cdc94922b1eb34b
  languageName: node
  linkType: hard

"@types/trusted-types@npm:^2.0.7":
  version: 2.0.7
  resolution: "@types/trusted-types@npm:2.0.7"
  checksum: 10c0/4c4855f10de7c6c135e0d32ce462419d8abbbc33713b31d294596c0cc34ae1fa6112a2f9da729c8f7a20707782b0d69da3b1f8df6645b0366d08825ca1522e0c
  languageName: node
  linkType: hard

"@types/unist@npm:*, @types/unist@npm:^3.0.0":
  version: 3.0.3
  resolution: "@types/unist@npm:3.0.3"
  checksum: 10c0/2b1e4adcab78388e088fcc3c0ae8700f76619dbcb4741d7d201f87e2cb346bfc29a89003cfea2d76c996e1061452e14fcd737e8b25aacf949c1f2d6b2bc3dd60
  languageName: node
  linkType: hard

"@types/unist@npm:^2, @types/unist@npm:^2.0.0":
  version: 2.0.11
  resolution: "@types/unist@npm:2.0.11"
  checksum: 10c0/24dcdf25a168f453bb70298145eb043cfdbb82472db0bc0b56d6d51cd2e484b9ed8271d4ac93000a80da568f2402e9339723db262d0869e2bf13bc58e081768d
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.0.0":
  version: 1.3.0
  resolution: "@ungap/structured-clone@npm:1.3.0"
  checksum: 10c0/0fc3097c2540ada1fc340ee56d58d96b5b536a2a0dab6e3ec17d4bfc8c4c86db345f61a375a8185f9da96f01c69678f836a2b57eeaa9e4b8eeafd26428e57b0a
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.0
  resolution: "abbrev@npm:3.0.0"
  checksum: 10c0/049704186396f571650eb7b22ed3627b77a5aedf98bb83caf2eac81ca2a3e25e795394b0464cfb2d6076df3db6a5312139eac5b6a126ca296ac53c5008069c28
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.0.0":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn@npm:^8.0.0, acorn@npm:^8.14.0":
  version: 8.14.0
  resolution: "acorn@npm:8.14.0"
  bin:
    acorn: bin/acorn
  checksum: 10c0/6d4ee461a7734b2f48836ee0fbb752903606e576cc100eb49340295129ca0b452f3ba91ddd4424a1d4406a98adfb2ebb6bd0ff4c49d7a0930c10e462719bbfd7
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10c0/6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.3.2":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10c0/da917be01871525a3dfcf925ae2977bc59e8c513d4423368645634bf5d4ceba5401574eb705c1e92b79f7292af5a656f78c5725a4b0e1cec97c4b413705c1d50
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10c0/b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"astring@npm:^1.8.0":
  version: 1.9.0
  resolution: "astring@npm:1.9.0"
  bin:
    astring: bin/astring
  checksum: 10c0/e7519544d9824494e80ef0e722bb3a0c543a31440d59691c13aeaceb75b14502af536b23f08db50aa6c632dafaade54caa25f0788aa7550b6b2d6e2df89e0830
  languageName: node
  linkType: hard

"async@npm:^3.2.3":
  version: 3.2.6
  resolution: "async@npm:3.2.6"
  checksum: 10c0/36484bb15ceddf07078688d95e27076379cc2f87b10c03b6dd8a83e89475a3c8df5848859dd06a4c95af1e4c16fc973de0171a77f18ea00be899aca2a4f85e70
  languageName: node
  linkType: hard

"bail@npm:^2.0.0":
  version: 2.0.2
  resolution: "bail@npm:2.0.2"
  checksum: 10c0/25cbea309ef6a1f56214187004e8f34014eb015713ea01fa5b9b7e9e776ca88d0fdffd64143ac42dc91966c915a4b7b683411b56e14929fad16153fc026ffb8b
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: 10c0/230520f1ff920b2d2ce3e372d77a33faa4fa60d802fe01ca4ffbc321ee06023fe9a741ac02793ee778040a16b7e497f7d60c504d1c402b8fdab6f03bb785a25f
  languageName: node
  linkType: hard

"body-scroll-lock@npm:4.0.0-beta.0":
  version: 4.0.0-beta.0
  resolution: "body-scroll-lock@npm:4.0.0-beta.0"
  checksum: 10c0/32a9553b83424e69f3784d7bbcef2949c43159ea13b5084c96dc08b1e2585e4eeb1e915f14e37a4ac44110339dfa6fa2ddde4d3d812be88ecbea060aa724a791
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"buffer-builder@npm:^0.2.0":
  version: 0.2.0
  resolution: "buffer-builder@npm:0.2.0"
  checksum: 10c0/e50c3a379f4acaea75ade1ee3e8c07ed6d7c5dfc3f98adbcf0159bfe1a4ce8ca1fe3689e861fcdb3fcef0012ebd4345a6112a5b8a1185295452bb66d7b6dc8a1
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-me-maybe@npm:^1.0.1":
  version: 1.0.2
  resolution: "call-me-maybe@npm:1.0.2"
  checksum: 10c0/8eff5dbb61141ebb236ed71b4e9549e488bcb5451c48c11e5667d5c75b0532303788a1101e6978cafa2d0c8c1a727805599c2741e3e0982855c9f1d78cd06c9f
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001616":
  version: 1.0.30001701
  resolution: "caniuse-lite@npm:1.0.30001701"
  checksum: 10c0/a814bd4dd8b49645ca51bc6ee42120660a36394bb54eb6084801d3f2bbb9471e5e1a9a8a25f44f83086a032d46e66b33031e2aa345f699b90a7e84a9836b819c
  languageName: node
  linkType: hard

"ccount@npm:^2.0.0":
  version: 2.0.1
  resolution: "ccount@npm:2.0.1"
  checksum: 10c0/3939b1664390174484322bc3f45b798462e6c07ee6384cb3d645e0aa2f318502d174845198c1561930e1d431087f74cf1fe291ae9a4722821a9f4ba67e574350
  languageName: node
  linkType: hard

"chalk@npm:^4.0.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"character-entities-html4@npm:^2.0.0":
  version: 2.1.0
  resolution: "character-entities-html4@npm:2.1.0"
  checksum: 10c0/fe61b553f083400c20c0b0fd65095df30a0b445d960f3bbf271536ae6c3ba676f39cb7af0b4bf2755812f08ab9b88f2feed68f9aebb73bb153f7a115fe5c6e40
  languageName: node
  linkType: hard

"character-entities-legacy@npm:^1.0.0":
  version: 1.1.4
  resolution: "character-entities-legacy@npm:1.1.4"
  checksum: 10c0/ea4ca9c29887335eed86d78fc67a640168342b1274da84c097abb0575a253d1265281a5052f9a863979e952bcc267b4ecaaf4fe233a7e1e0d8a47806c65b96c7
  languageName: node
  linkType: hard

"character-entities-legacy@npm:^3.0.0":
  version: 3.0.0
  resolution: "character-entities-legacy@npm:3.0.0"
  checksum: 10c0/ec4b430af873661aa754a896a2b55af089b4e938d3d010fad5219299a6b6d32ab175142699ee250640678cd64bdecd6db3c9af0b8759ab7b155d970d84c4c7d1
  languageName: node
  linkType: hard

"character-entities@npm:^1.0.0":
  version: 1.2.4
  resolution: "character-entities@npm:1.2.4"
  checksum: 10c0/ad015c3d7163563b8a0ee1f587fb0ef305ef344e9fd937f79ca51cccc233786a01d591d989d5bf7b2e66b528ac9efba47f3b1897358324e69932f6d4b25adfe1
  languageName: node
  linkType: hard

"character-entities@npm:^2.0.0":
  version: 2.0.2
  resolution: "character-entities@npm:2.0.2"
  checksum: 10c0/b0c645a45bcc90ff24f0e0140f4875a8436b8ef13b6bcd31ec02cfb2ca502b680362aa95386f7815bdc04b6464d48cf191210b3840d7c04241a149ede591a308
  languageName: node
  linkType: hard

"character-reference-invalid@npm:^1.0.0":
  version: 1.1.4
  resolution: "character-reference-invalid@npm:1.1.4"
  checksum: 10c0/29f05081c5817bd1e975b0bf61e77b60a40f62ad371d0f0ce0fdb48ab922278bc744d1fbe33771dced751887a8403f265ff634542675c8d7375f6ff4811efd0e
  languageName: node
  linkType: hard

"character-reference-invalid@npm:^2.0.0":
  version: 2.0.1
  resolution: "character-reference-invalid@npm:2.0.1"
  checksum: 10c0/2ae0dec770cd8659d7e8b0ce24392d83b4c2f0eb4a3395c955dce5528edd4cc030a794cfa06600fcdd700b3f2de2f9b8e40e309c0011c4180e3be64a0b42e6a1
  languageName: node
  linkType: hard

"chardet@npm:^0.7.0":
  version: 0.7.0
  resolution: "chardet@npm:0.7.0"
  checksum: 10c0/96e4731b9ec8050cbb56ab684e8c48d6c33f7826b755802d14e3ebfdc51c57afeece3ea39bc6b09acc359e4363525388b915e16640c1378053820f5e70d0f27d
  languageName: node
  linkType: hard

"chevrotain-allstar@npm:~0.3.0":
  version: 0.3.1
  resolution: "chevrotain-allstar@npm:0.3.1"
  dependencies:
    lodash-es: "npm:^4.17.21"
  peerDependencies:
    chevrotain: ^11.0.0
  checksum: 10c0/5cadedffd3114eb06b15fd3939bb1aa6c75412dbd737fe302b52c5c24334f9cb01cee8edc1d1067d98ba80dddf971f1d0e94b387de51423fc6cf3c5d8b7ef27a
  languageName: node
  linkType: hard

"chevrotain@npm:~11.0.3":
  version: 11.0.3
  resolution: "chevrotain@npm:11.0.3"
  dependencies:
    "@chevrotain/cst-dts-gen": "npm:11.0.3"
    "@chevrotain/gast": "npm:11.0.3"
    "@chevrotain/regexp-to-ast": "npm:11.0.3"
    "@chevrotain/types": "npm:11.0.3"
    "@chevrotain/utils": "npm:11.0.3"
    lodash-es: "npm:4.17.21"
  checksum: 10c0/ffd425fa321e3f17e9833d7f44cd39f2743f066e92ca74b226176080ca5d455f853fe9091cdfd86354bd899d85c08b3bdc3f55b267e7d07124b048a88349765f
  languageName: node
  linkType: hard

"chokidar@npm:^4.0.3":
  version: 4.0.3
  resolution: "chokidar@npm:4.0.3"
  dependencies:
    readdirp: "npm:^4.0.1"
  checksum: 10c0/a58b9df05bb452f7d105d9e7229ac82fa873741c0c40ddcc7bb82f8a909fbe3f7814c9ebe9bc9a2bef9b737c0ec6e2d699d179048ef06ad3ec46315df0ebe6ad
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"cli-progress@npm:^3.12.0":
  version: 3.12.0
  resolution: "cli-progress@npm:3.12.0"
  dependencies:
    string-width: "npm:^4.2.3"
  checksum: 10c0/f464cb19ebde2f3880620a2adfaeeefaec6cb15c8e610c8a659ca1047ee90d69f3bf2fdabbb1fe33ac408678e882e3e0eecdb84ab5df0edf930b269b8a72682d
  languageName: node
  linkType: hard

"cli-width@npm:^4.1.0":
  version: 4.1.0
  resolution: "cli-width@npm:4.1.0"
  checksum: 10c0/1fbd56413578f6117abcaf858903ba1f4ad78370a4032f916745fa2c7e390183a9d9029cf837df320b0fdce8137668e522f60a30a5f3d6529ff3872d265a955f
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/4bda0f09c340cbb6dfdc1ed508b3ca080f12992c18d68c6be4d9cf51756033d5266e61ec57529e610dacbf4da1c634423b0c1b11037709cc6b09045cbd815df5
  languageName: node
  linkType: hard

"clsx@npm:^2.1.1":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: 10c0/c4c8eb865f8c82baab07e71bfa8897c73454881c4f99d6bc81585aecd7c441746c1399d08363dc096c550cceaf97bd4ce1e8854e1771e9998d9f94c4fe075839
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"colorjs.io@npm:^0.5.0":
  version: 0.5.2
  resolution: "colorjs.io@npm:0.5.2"
  checksum: 10c0/2e6ea43629e325e721b92429239de3a6f42fb6d88ba6e4c2aeff0288c196d876f2f7ee82aea95bd40072d5cdc8cb87f042f4d94c134dcabf0e34a717e4caacb9
  languageName: node
  linkType: hard

"comma-separated-tokens@npm:^1.0.0":
  version: 1.0.8
  resolution: "comma-separated-tokens@npm:1.0.8"
  checksum: 10c0/c3bcfeaa6d50313528a006a40bcc0f9576086665c9b48d4b3a76ddd63e7d6174734386c98be1881cbf6ecfc25e1db61cd775a7b896d2ea7a65de28f83a0f9b17
  languageName: node
  linkType: hard

"comma-separated-tokens@npm:^2.0.0":
  version: 2.0.3
  resolution: "comma-separated-tokens@npm:2.0.3"
  checksum: 10c0/91f90f1aae320f1755d6957ef0b864fe4f54737f3313bd95e0802686ee2ca38bff1dd381964d00ae5db42912dd1f4ae5c2709644e82706ffc6f6842a813cdd67
  languageName: node
  linkType: hard

"commander@npm:7":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 10c0/8d690ff13b0356df7e0ebbe6c59b4712f754f4b724d4f473d3cc5b3fdcf978e3a5dc3078717858a2ceb50b0f84d0660a7f22a96cdc50fb877d0c9bb31593d23a
  languageName: node
  linkType: hard

"commander@npm:^11.1.0":
  version: 11.1.0
  resolution: "commander@npm:11.1.0"
  checksum: 10c0/13cc6ac875e48780250f723fb81c1c1178d35c5decb1abb1b628b3177af08a8554e76b2c0f29de72d69eef7c864d12613272a71fabef8047922bc622ab75a179
  languageName: node
  linkType: hard

"commander@npm:^13.1.0":
  version: 13.1.0
  resolution: "commander@npm:13.1.0"
  checksum: 10c0/7b8c5544bba704fbe84b7cab2e043df8586d5c114a4c5b607f83ae5060708940ed0b5bd5838cf8ce27539cde265c1cbd59ce3c8c6b017ed3eec8943e3a415164
  languageName: node
  linkType: hard

"commander@npm:^8.3.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 10c0/8b043bb8322ea1c39664a1598a95e0495bfe4ca2fad0d84a92d7d1d8d213e2a155b441d2470c8e08de7c4a28cf2bc6e169211c49e1b21d9f7edc6ae4d9356060
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"confbox@npm:^0.1.8":
  version: 0.1.8
  resolution: "confbox@npm:0.1.8"
  checksum: 10c0/fc2c68d97cb54d885b10b63e45bd8da83a8a71459d3ecf1825143dd4c7f9f1b696b3283e07d9d12a144c1301c2ebc7842380bdf0014e55acc4ae1c9550102418
  languageName: node
  linkType: hard

"copy-to-clipboard@npm:^3.3.3":
  version: 3.3.3
  resolution: "copy-to-clipboard@npm:3.3.3"
  dependencies:
    toggle-selection: "npm:^1.0.6"
  checksum: 10c0/3ebf5e8ee00601f8c440b83ec08d838e8eabb068c1fae94a9cda6b42f288f7e1b552f3463635f419af44bf7675afc8d0390d30876cf5c2d5d35f86d9c56a3e5f
  languageName: node
  linkType: hard

"core-js@npm:~3.40.0":
  version: 3.40.0
  resolution: "core-js@npm:3.40.0"
  checksum: 10c0/db7946ada881e845d8b157061945b1187618fa45cf162f392a151e8a497962aed2da688c982eaa1d444c864be97a70f8be4d73385294b515d224dd164d19f1d4
  languageName: node
  linkType: hard

"cose-base@npm:^1.0.0":
  version: 1.0.3
  resolution: "cose-base@npm:1.0.3"
  dependencies:
    layout-base: "npm:^1.0.0"
  checksum: 10c0/a6e400b1d101393d6af0967c1353355777c1106c40417c5acaef6ca8bdda41e2fc9398f466d6c85be30290943ad631f2590569f67b3fd5368a0d8318946bd24f
  languageName: node
  linkType: hard

"cose-base@npm:^2.2.0":
  version: 2.2.0
  resolution: "cose-base@npm:2.2.0"
  dependencies:
    layout-base: "npm:^2.0.0"
  checksum: 10c0/14b9f8100ac322a00777ffb1daeb3321af368bbc9cabe3103943361273baee2003202ffe38e4ab770960b600214224e9c196195a78d589521540aa694df7cdec
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10c0/80c089d6f7e0c5b2bd83cf0539ab41474198579584fa10d86d0cafe0642202343cbc119e076a0b1aece191989477081415d66c9fefbf3c957fc2fc4b7009f248
  languageName: node
  linkType: hard

"cytoscape-cose-bilkent@npm:^4.1.0":
  version: 4.1.0
  resolution: "cytoscape-cose-bilkent@npm:4.1.0"
  dependencies:
    cose-base: "npm:^1.0.0"
  peerDependencies:
    cytoscape: ^3.2.0
  checksum: 10c0/5e2480ddba9da1a68e700ed2c674cbfd51e9efdbd55788f1971a68de4eb30708e3b3a5e808bf5628f7a258680406bbe6586d87a9133e02a9bdc1ab1a92f512f2
  languageName: node
  linkType: hard

"cytoscape-fcose@npm:^2.2.0":
  version: 2.2.0
  resolution: "cytoscape-fcose@npm:2.2.0"
  dependencies:
    cose-base: "npm:^2.2.0"
  peerDependencies:
    cytoscape: ^3.2.0
  checksum: 10c0/ce472c9f85b9057e75c5685396f8e1f2468895e71b184913e05ad56dcf3092618fe59a1054f29cb0995051ba8ebe566ad0dd49a58d62845145624bd60cd44917
  languageName: node
  linkType: hard

"cytoscape@npm:^3.29.3":
  version: 3.31.1
  resolution: "cytoscape@npm:3.31.1"
  checksum: 10c0/7c439561be055c3979d4e724daff398f3a0f752ffa14086952e5466e38dfc7a178f451928a90bca58971665db1fecda5777abb8c74f83ef68cfaabb08c204b19
  languageName: node
  linkType: hard

"d3-array@npm:1 - 2":
  version: 2.12.1
  resolution: "d3-array@npm:2.12.1"
  dependencies:
    internmap: "npm:^1.0.0"
  checksum: 10c0/7eca10427a9f113a4ca6a0f7301127cab26043fd5e362631ef5a0edd1c4b2dd70c56ed317566700c31e4a6d88b55f3951aaba192291817f243b730cb2352882e
  languageName: node
  linkType: hard

"d3-array@npm:2 - 3, d3-array@npm:2.10.0 - 3, d3-array@npm:2.5.0 - 3, d3-array@npm:3, d3-array@npm:^3.2.0":
  version: 3.2.4
  resolution: "d3-array@npm:3.2.4"
  dependencies:
    internmap: "npm:1 - 2"
  checksum: 10c0/08b95e91130f98c1375db0e0af718f4371ccacef7d5d257727fe74f79a24383e79aba280b9ffae655483ffbbad4fd1dec4ade0119d88c4749f388641c8bf8c50
  languageName: node
  linkType: hard

"d3-axis@npm:3":
  version: 3.0.0
  resolution: "d3-axis@npm:3.0.0"
  checksum: 10c0/a271e70ba1966daa5aaf6a7f959ceca3e12997b43297e757c7b945db2e1ead3c6ee226f2abcfa22abbd4e2e28bd2b71a0911794c4e5b911bbba271328a582c78
  languageName: node
  linkType: hard

"d3-brush@npm:3":
  version: 3.0.0
  resolution: "d3-brush@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-drag: "npm:2 - 3"
    d3-interpolate: "npm:1 - 3"
    d3-selection: "npm:3"
    d3-transition: "npm:3"
  checksum: 10c0/07baf00334c576da2f68a91fc0da5732c3a5fa19bd3d7aed7fd24d1d674a773f71a93e9687c154176f7246946194d77c48c2d8fed757f5dcb1a4740067ec50a8
  languageName: node
  linkType: hard

"d3-chord@npm:3":
  version: 3.0.1
  resolution: "d3-chord@npm:3.0.1"
  dependencies:
    d3-path: "npm:1 - 3"
  checksum: 10c0/baa6013914af3f4fe1521f0d16de31a38eb8a71d08ff1dec4741f6f45a828661e5cd3935e39bd14e3032bdc78206c283ca37411da21d46ec3cfc520be6e7a7ce
  languageName: node
  linkType: hard

"d3-color@npm:1 - 3, d3-color@npm:3":
  version: 3.1.0
  resolution: "d3-color@npm:3.1.0"
  checksum: 10c0/a4e20e1115fa696fce041fbe13fbc80dc4c19150fa72027a7c128ade980bc0eeeba4bcf28c9e21f0bce0e0dbfe7ca5869ef67746541dcfda053e4802ad19783c
  languageName: node
  linkType: hard

"d3-contour@npm:4":
  version: 4.0.2
  resolution: "d3-contour@npm:4.0.2"
  dependencies:
    d3-array: "npm:^3.2.0"
  checksum: 10c0/98bc5fbed6009e08707434a952076f39f1cd6ed8b9288253cc3e6a3286e4e80c63c62d84954b20e64bf6e4ededcc69add54d3db25e990784a59c04edd3449032
  languageName: node
  linkType: hard

"d3-delaunay@npm:6":
  version: 6.0.4
  resolution: "d3-delaunay@npm:6.0.4"
  dependencies:
    delaunator: "npm:5"
  checksum: 10c0/57c3aecd2525664b07c4c292aa11cf49b2752c0cf3f5257f752999399fe3c592de2d418644d79df1f255471eec8057a9cc0c3062ed7128cb3348c45f69597754
  languageName: node
  linkType: hard

"d3-dispatch@npm:1 - 3, d3-dispatch@npm:3":
  version: 3.0.1
  resolution: "d3-dispatch@npm:3.0.1"
  checksum: 10c0/6eca77008ce2dc33380e45d4410c67d150941df7ab45b91d116dbe6d0a3092c0f6ac184dd4602c796dc9e790222bad3ff7142025f5fd22694efe088d1d941753
  languageName: node
  linkType: hard

"d3-drag@npm:2 - 3, d3-drag@npm:3":
  version: 3.0.0
  resolution: "d3-drag@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-selection: "npm:3"
  checksum: 10c0/d2556e8dc720741a443b595a30af403dd60642dfd938d44d6e9bfc4c71a962142f9a028c56b61f8b4790b65a34acad177d1263d66f103c3c527767b0926ef5aa
  languageName: node
  linkType: hard

"d3-dsv@npm:1 - 3, d3-dsv@npm:3":
  version: 3.0.1
  resolution: "d3-dsv@npm:3.0.1"
  dependencies:
    commander: "npm:7"
    iconv-lite: "npm:0.6"
    rw: "npm:1"
  bin:
    csv2json: bin/dsv2json.js
    csv2tsv: bin/dsv2dsv.js
    dsv2dsv: bin/dsv2dsv.js
    dsv2json: bin/dsv2json.js
    json2csv: bin/json2dsv.js
    json2dsv: bin/json2dsv.js
    json2tsv: bin/json2dsv.js
    tsv2csv: bin/dsv2dsv.js
    tsv2json: bin/dsv2json.js
  checksum: 10c0/10e6af9e331950ed258f34ab49ac1b7060128ef81dcf32afc790bd1f7e8c3cc2aac7f5f875250a83f21f39bb5925fbd0872bb209f8aca32b3b77d32bab8a65ab
  languageName: node
  linkType: hard

"d3-ease@npm:1 - 3, d3-ease@npm:3":
  version: 3.0.1
  resolution: "d3-ease@npm:3.0.1"
  checksum: 10c0/fec8ef826c0cc35cda3092c6841e07672868b1839fcaf556e19266a3a37e6bc7977d8298c0fcb9885e7799bfdcef7db1baaba9cd4dcf4bc5e952cf78574a88b0
  languageName: node
  linkType: hard

"d3-fetch@npm:3":
  version: 3.0.1
  resolution: "d3-fetch@npm:3.0.1"
  dependencies:
    d3-dsv: "npm:1 - 3"
  checksum: 10c0/4f467a79bf290395ac0cbb5f7562483f6a18668adc4c8eb84c9d3eff048b6f6d3b6f55079ba1ebf1908dabe000c941d46be447f8d78453b2dad5fb59fb6aa93b
  languageName: node
  linkType: hard

"d3-force@npm:3":
  version: 3.0.0
  resolution: "d3-force@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-quadtree: "npm:1 - 3"
    d3-timer: "npm:1 - 3"
  checksum: 10c0/220a16a1a1ac62ba56df61028896e4b52be89c81040d20229c876efc8852191482c233f8a52bb5a4e0875c321b8e5cb6413ef3dfa4d8fe79eeb7d52c587f52cf
  languageName: node
  linkType: hard

"d3-format@npm:1 - 3, d3-format@npm:3":
  version: 3.1.0
  resolution: "d3-format@npm:3.1.0"
  checksum: 10c0/049f5c0871ebce9859fc5e2f07f336b3c5bfff52a2540e0bac7e703fce567cd9346f4ad1079dd18d6f1e0eaa0599941c1810898926f10ac21a31fd0a34b4aa75
  languageName: node
  linkType: hard

"d3-geo@npm:3":
  version: 3.1.1
  resolution: "d3-geo@npm:3.1.1"
  dependencies:
    d3-array: "npm:2.5.0 - 3"
  checksum: 10c0/d32270dd2dc8ac3ea63e8805d63239c4c8ec6c0d339d73b5e5a30a87f8f54db22a78fb434369799465eae169503b25f9a107c642c8a16c32a3285bc0e6d8e8c1
  languageName: node
  linkType: hard

"d3-hierarchy@npm:3":
  version: 3.1.2
  resolution: "d3-hierarchy@npm:3.1.2"
  checksum: 10c0/6dcdb480539644aa7fc0d72dfc7b03f99dfbcdf02714044e8c708577e0d5981deb9d3e99bbbb2d26422b55bcc342ac89a0fa2ea6c9d7302e2fc0951dd96f89cf
  languageName: node
  linkType: hard

"d3-interpolate@npm:1 - 3, d3-interpolate@npm:1.2.0 - 3, d3-interpolate@npm:3":
  version: 3.0.1
  resolution: "d3-interpolate@npm:3.0.1"
  dependencies:
    d3-color: "npm:1 - 3"
  checksum: 10c0/19f4b4daa8d733906671afff7767c19488f51a43d251f8b7f484d5d3cfc36c663f0a66c38fe91eee30f40327443d799be17169f55a293a3ba949e84e57a33e6a
  languageName: node
  linkType: hard

"d3-path@npm:1":
  version: 1.0.9
  resolution: "d3-path@npm:1.0.9"
  checksum: 10c0/e35e84df5abc18091f585725b8235e1fa97efc287571585427d3a3597301e6c506dea56b11dfb3c06ca5858b3eb7f02c1bf4f6a716aa9eade01c41b92d497eb5
  languageName: node
  linkType: hard

"d3-path@npm:1 - 3, d3-path@npm:3, d3-path@npm:^3.1.0":
  version: 3.1.0
  resolution: "d3-path@npm:3.1.0"
  checksum: 10c0/dc1d58ec87fa8319bd240cf7689995111a124b141428354e9637aa83059eb12e681f77187e0ada5dedfce346f7e3d1f903467ceb41b379bfd01cd8e31721f5da
  languageName: node
  linkType: hard

"d3-polygon@npm:3":
  version: 3.0.1
  resolution: "d3-polygon@npm:3.0.1"
  checksum: 10c0/e236aa7f33efa9a4072907af7dc119f85b150a0716759d4fe5f12f62573018264a6cbde8617fbfa6944a7ae48c1c0c8d3f39ae72e11f66dd471e9b5e668385df
  languageName: node
  linkType: hard

"d3-quadtree@npm:1 - 3, d3-quadtree@npm:3":
  version: 3.0.1
  resolution: "d3-quadtree@npm:3.0.1"
  checksum: 10c0/18302d2548bfecaef788152397edec95a76400fd97d9d7f42a089ceb68d910f685c96579d74e3712d57477ed042b056881b47cd836a521de683c66f47ce89090
  languageName: node
  linkType: hard

"d3-random@npm:3":
  version: 3.0.1
  resolution: "d3-random@npm:3.0.1"
  checksum: 10c0/987a1a1bcbf26e6cf01fd89d5a265b463b2cea93560fc17d9b1c45e8ed6ff2db5924601bcceb808de24c94133f000039eb7fa1c469a7a844ccbf1170cbb25b41
  languageName: node
  linkType: hard

"d3-sankey@npm:^0.12.3":
  version: 0.12.3
  resolution: "d3-sankey@npm:0.12.3"
  dependencies:
    d3-array: "npm:1 - 2"
    d3-shape: "npm:^1.2.0"
  checksum: 10c0/261debb01a13269f6fc53b9ebaef174a015d5ad646242c23995bf514498829ab8b8f920a7873724a7494288b46bea3ce7ebc5a920b745bc8ae4caa5885cf5204
  languageName: node
  linkType: hard

"d3-scale-chromatic@npm:3":
  version: 3.1.0
  resolution: "d3-scale-chromatic@npm:3.1.0"
  dependencies:
    d3-color: "npm:1 - 3"
    d3-interpolate: "npm:1 - 3"
  checksum: 10c0/9a3f4671ab0b971f4a411b42180d7cf92bfe8e8584e637ce7e698d705e18d6d38efbd20ec64f60cc0dfe966c20d40fc172565bc28aaa2990c0a006360eed91af
  languageName: node
  linkType: hard

"d3-scale@npm:4":
  version: 4.0.2
  resolution: "d3-scale@npm:4.0.2"
  dependencies:
    d3-array: "npm:2.10.0 - 3"
    d3-format: "npm:1 - 3"
    d3-interpolate: "npm:1.2.0 - 3"
    d3-time: "npm:2.1.1 - 3"
    d3-time-format: "npm:2 - 4"
  checksum: 10c0/65d9ad8c2641aec30ed5673a7410feb187a224d6ca8d1a520d68a7d6eac9d04caedbff4713d1e8545be33eb7fec5739983a7ab1d22d4e5ad35368c6729d362f1
  languageName: node
  linkType: hard

"d3-selection@npm:2 - 3, d3-selection@npm:3":
  version: 3.0.0
  resolution: "d3-selection@npm:3.0.0"
  checksum: 10c0/e59096bbe8f0cb0daa1001d9bdd6dbc93a688019abc97d1d8b37f85cd3c286a6875b22adea0931b0c88410d025563e1643019161a883c516acf50c190a11b56b
  languageName: node
  linkType: hard

"d3-shape@npm:3":
  version: 3.2.0
  resolution: "d3-shape@npm:3.2.0"
  dependencies:
    d3-path: "npm:^3.1.0"
  checksum: 10c0/f1c9d1f09926daaf6f6193ae3b4c4b5521e81da7d8902d24b38694517c7f527ce3c9a77a9d3a5722ad1e3ff355860b014557b450023d66a944eabf8cfde37132
  languageName: node
  linkType: hard

"d3-shape@npm:^1.2.0":
  version: 1.3.7
  resolution: "d3-shape@npm:1.3.7"
  dependencies:
    d3-path: "npm:1"
  checksum: 10c0/548057ce59959815decb449f15632b08e2a1bdce208f9a37b5f98ec7629dda986c2356bc7582308405ce68aedae7d47b324df41507404df42afaf352907577ae
  languageName: node
  linkType: hard

"d3-time-format@npm:2 - 4, d3-time-format@npm:4":
  version: 4.1.0
  resolution: "d3-time-format@npm:4.1.0"
  dependencies:
    d3-time: "npm:1 - 3"
  checksum: 10c0/735e00fb25a7fd5d418fac350018713ae394eefddb0d745fab12bbff0517f9cdb5f807c7bbe87bb6eeb06249662f8ea84fec075f7d0cd68609735b2ceb29d206
  languageName: node
  linkType: hard

"d3-time@npm:1 - 3, d3-time@npm:2.1.1 - 3, d3-time@npm:3":
  version: 3.1.0
  resolution: "d3-time@npm:3.1.0"
  dependencies:
    d3-array: "npm:2 - 3"
  checksum: 10c0/a984f77e1aaeaa182679b46fbf57eceb6ebdb5f67d7578d6f68ef933f8eeb63737c0949991618a8d29472dbf43736c7d7f17c452b2770f8c1271191cba724ca1
  languageName: node
  linkType: hard

"d3-timer@npm:1 - 3, d3-timer@npm:3":
  version: 3.0.1
  resolution: "d3-timer@npm:3.0.1"
  checksum: 10c0/d4c63cb4bb5461d7038aac561b097cd1c5673969b27cbdd0e87fa48d9300a538b9e6f39b4a7f0e3592ef4f963d858c8a9f0e92754db73116770856f2fc04561a
  languageName: node
  linkType: hard

"d3-transition@npm:2 - 3, d3-transition@npm:3":
  version: 3.0.1
  resolution: "d3-transition@npm:3.0.1"
  dependencies:
    d3-color: "npm:1 - 3"
    d3-dispatch: "npm:1 - 3"
    d3-ease: "npm:1 - 3"
    d3-interpolate: "npm:1 - 3"
    d3-timer: "npm:1 - 3"
  peerDependencies:
    d3-selection: 2 - 3
  checksum: 10c0/4e74535dda7024aa43e141635b7522bb70cf9d3dfefed975eb643b36b864762eca67f88fafc2ca798174f83ca7c8a65e892624f824b3f65b8145c6a1a88dbbad
  languageName: node
  linkType: hard

"d3-zoom@npm:3":
  version: 3.0.0
  resolution: "d3-zoom@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-drag: "npm:2 - 3"
    d3-interpolate: "npm:1 - 3"
    d3-selection: "npm:2 - 3"
    d3-transition: "npm:2 - 3"
  checksum: 10c0/ee2036479049e70d8c783d594c444fe00e398246048e3f11a59755cd0e21de62ece3126181b0d7a31bf37bcf32fd726f83ae7dea4495ff86ec7736ce5ad36fd3
  languageName: node
  linkType: hard

"d3@npm:^7.9.0":
  version: 7.9.0
  resolution: "d3@npm:7.9.0"
  dependencies:
    d3-array: "npm:3"
    d3-axis: "npm:3"
    d3-brush: "npm:3"
    d3-chord: "npm:3"
    d3-color: "npm:3"
    d3-contour: "npm:4"
    d3-delaunay: "npm:6"
    d3-dispatch: "npm:3"
    d3-drag: "npm:3"
    d3-dsv: "npm:3"
    d3-ease: "npm:3"
    d3-fetch: "npm:3"
    d3-force: "npm:3"
    d3-format: "npm:3"
    d3-geo: "npm:3"
    d3-hierarchy: "npm:3"
    d3-interpolate: "npm:3"
    d3-path: "npm:3"
    d3-polygon: "npm:3"
    d3-quadtree: "npm:3"
    d3-random: "npm:3"
    d3-scale: "npm:4"
    d3-scale-chromatic: "npm:3"
    d3-selection: "npm:3"
    d3-shape: "npm:3"
    d3-time: "npm:3"
    d3-time-format: "npm:4"
    d3-timer: "npm:3"
    d3-transition: "npm:3"
    d3-zoom: "npm:3"
  checksum: 10c0/3dd9c08c73cfaa69c70c49e603c85e049c3904664d9c79a1a52a0f52795828a1ff23592dc9a7b2257e711d68a615472a13103c212032f38e016d609796e087e8
  languageName: node
  linkType: hard

"dagre-d3-es@npm:7.0.11":
  version: 7.0.11
  resolution: "dagre-d3-es@npm:7.0.11"
  dependencies:
    d3: "npm:^7.9.0"
    lodash-es: "npm:^4.17.21"
  checksum: 10c0/52f88bdfeca0d8554bee0c1419377585355b4ef179e5fedd3bac75f772745ecb789f6d7ea377a17566506bc8f151bc0dfe02a5175207a547975f335cd88c726c
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.13":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: 10c0/a3caf6ac8363c7dade9d1ee797848ddcf25c1ace68d9fe8678ecf8ba0675825430de5d793672ec87b24a69bf04a1544b176547b2539982275d5542a7955f35b7
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.0.0, debug@npm:^4.1.1, debug@npm:^4.3.4, debug@npm:^4.3.5, debug@npm:^4.4.0":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/db94f1a182bf886f57b4755f85b3a74c39b5114b9377b7ab375dc2cfa3454f09490cc6c30f829df3fc8042bc8b8995f6567ce5cd96f3bc3688bd24027197d9de
  languageName: node
  linkType: hard

"decode-named-character-reference@npm:^1.0.0":
  version: 1.0.2
  resolution: "decode-named-character-reference@npm:1.0.2"
  dependencies:
    character-entities: "npm:^2.0.0"
  checksum: 10c0/66a9fc5d9b5385a2b3675c69ba0d8e893393d64057f7dbbb585265bb4fc05ec513d76943b8e5aac7d8016d20eea4499322cbf4cd6d54b466976b78f3a7587a4c
  languageName: node
  linkType: hard

"deepmerge@npm:^4.3.1":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 10c0/e53481aaf1aa2c4082b5342be6b6d8ad9dfe387bc92ce197a66dea08bd4265904a087e75e464f14d1347cf2ac8afe1e4c16b266e0561cc5df29382d3c5f80044
  languageName: node
  linkType: hard

"delaunator@npm:5":
  version: 5.0.1
  resolution: "delaunator@npm:5.0.1"
  dependencies:
    robust-predicates: "npm:^3.0.2"
  checksum: 10c0/3d7ea4d964731c5849af33fec0a271bc6753487b331fd7d43ccb17d77834706e1c383e6ab8fda0032da955e7576d1083b9603cdaf9cbdfd6b3ebd1fb8bb675a5
  languageName: node
  linkType: hard

"dequal@npm:^2.0.0":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 10c0/f98860cdf58b64991ae10205137c0e97d384c3a4edc7f807603887b7c4b850af1224a33d88012009f150861cbee4fa2d322c4cc04b9313bee312e47f6ecaa888
  languageName: node
  linkType: hard

"devlop@npm:^1.0.0, devlop@npm:^1.1.0":
  version: 1.1.0
  resolution: "devlop@npm:1.1.0"
  dependencies:
    dequal: "npm:^2.0.0"
  checksum: 10c0/e0928ab8f94c59417a2b8389c45c55ce0a02d9ac7fd74ef62d01ba48060129e1d594501b77de01f3eeafc7cb00773819b0df74d96251cf20b31c5b3071f45c0e
  languageName: node
  linkType: hard

"diff@npm:^5.0.0":
  version: 5.2.0
  resolution: "diff@npm:5.2.0"
  checksum: 10c0/aed0941f206fe261ecb258dc8d0ceea8abbde3ace5827518ff8d302f0fc9cc81ce116c4d8f379151171336caf0516b79e01abdc1ed1201b6440d895a66689eb4
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.4.1
  resolution: "dom-serializer@npm:1.4.1"
  dependencies:
    domelementtype: "npm:^2.0.1"
    domhandler: "npm:^4.2.0"
    entities: "npm:^2.0.0"
  checksum: 10c0/67d775fa1ea3de52035c98168ddcd59418356943b5eccb80e3c8b3da53adb8e37edb2cc2f885802b7b1765bf5022aec21dfc32910d7f9e6de4c3148f095ab5e0
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: 10c0/d5ae2b7110ca3746b3643d3ef60ef823f5f078667baf530cec096433f1627ec4b6fa8c072f09d079d7cda915fd2c7bc1b7b935681e9b09e591e1e15f4040b8e2
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0, domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10c0/686f5a9ef0fff078c1412c05db73a0dce096190036f33e400a07e2a4518e9f56b1e324f5c576a0a747ef0e75b5d985c040b0d51945ce780c0dd3c625a18cd8c9
  languageName: node
  linkType: hard

"domhandler@npm:^4.0.0, domhandler@npm:^4.2.0":
  version: 4.3.1
  resolution: "domhandler@npm:4.3.1"
  dependencies:
    domelementtype: "npm:^2.2.0"
  checksum: 10c0/5c199c7468cb052a8b5ab80b13528f0db3d794c64fc050ba793b574e158e67c93f8336e87fd81e9d5ee43b0e04aea4d8b93ed7be4899cb726a1601b3ba18538b
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 10c0/bba1e5932b3e196ad6862286d76adc89a0dbf0c773e5ced1eb01f9af930c50093a084eff14b8de5ea60b895c56a04d5de8bbc4930c5543d029091916770b2d2a
  languageName: node
  linkType: hard

"dompurify@npm:^3.2.4":
  version: 3.2.4
  resolution: "dompurify@npm:3.2.4"
  dependencies:
    "@types/trusted-types": "npm:^2.0.7"
  dependenciesMeta:
    "@types/trusted-types":
      optional: true
  checksum: 10c0/6be56810fb7ad2776155c8fc2967af5056783c030094362c7d0cf1ad13f2129cf922d8eefab528a34bdebfb98e2f44b306a983ab93aefb9d6f24c18a3d027a05
  languageName: node
  linkType: hard

"domutils@npm:^2.5.2":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: "npm:^1.0.1"
    domelementtype: "npm:^2.2.0"
    domhandler: "npm:^4.2.0"
  checksum: 10c0/d58e2ae01922f0dd55894e61d18119924d88091837887bf1438f2327f32c65eb76426bd9384f81e7d6dcfb048e0f83c19b222ad7101176ad68cdc9c695b563db
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 10c0/47938f473b987ea71cd59e59626eb8666d3aa8feba5266e45527f3b636c7883cca7e582d901531961f742c519d7514636b7973353b648762b2e3bedbf235fada
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ejs@npm:^3.1.10":
  version: 3.1.10
  resolution: "ejs@npm:3.1.10"
  dependencies:
    jake: "npm:^10.8.5"
  bin:
    ejs: bin/cli.js
  checksum: 10c0/52eade9e68416ed04f7f92c492183340582a36482836b11eab97b159fcdcfdedc62233a1bf0bf5e5e1851c501f2dca0e2e9afd111db2599e4e7f53ee29429ae1
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: 10c0/7dc4394b7b910444910ad64b812392159a21e1a7ecc637c775a440227dcb4f80eff7fe61f4453a7d7603fa23d23d30cc93fe9e4b5ed985b88d6441cd4a35117b
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"enhanced-resolve@npm:5.18.0":
  version: 5.18.0
  resolution: "enhanced-resolve@npm:5.18.0"
  dependencies:
    graceful-fs: "npm:^4.2.4"
    tapable: "npm:^2.2.0"
  checksum: 10c0/5fcc264a6040754ab5b349628cac2bb5f89cee475cbe340804e657a5b9565f70e6aafb338d5895554eb0ced9f66c50f38a255274a0591dcb64ee17c549c459ce
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 10c0/7fba6af1f116300d2ba1c5673fc218af1961b20908638391b4e1e6d5850314ee2ac3ec22d741b3a8060479911c99305164aed19b6254bde75e7e6b1b2c3f3aa3
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.4.0, entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10c0/5b039739f7621f5d1ad996715e53d964035f75ad3b9a4d38c6b3804bb226e282ffeae2443624d8fdd9c47d8e926ae9ac009c54671243f0c3294c26af7cc85250
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"error-stack-parser@npm:^2.1.4":
  version: 2.1.4
  resolution: "error-stack-parser@npm:2.1.4"
  dependencies:
    stackframe: "npm:^1.3.4"
  checksum: 10c0/7679b780043c98b01fc546725484e0cfd3071bf5c906bbe358722972f04abf4fc3f0a77988017665bab367f6ef3fc2d0185f7528f45966b83e7c99c02d5509b9
  languageName: node
  linkType: hard

"es-toolkit@npm:^1.37.2":
  version: 1.37.2
  resolution: "es-toolkit@npm:1.37.2"
  dependenciesMeta:
    "@trivago/prettier-plugin-sort-imports@4.3.0":
      unplugged: true
    prettier-plugin-sort-re-exports@0.0.1:
      unplugged: true
  checksum: 10c0/b00746dc3e93b42adafea7d9a7605ebbe4c8fcc391b803fcb88c6f21151c6ec0f4cc2054292a38f5683b24bfa52330b78d59106883bc06ae294d5e66740625b9
  languageName: node
  linkType: hard

"es6-promise@npm:^3.2.1":
  version: 3.3.1
  resolution: "es6-promise@npm:3.3.1"
  checksum: 10c0/b4fc87cb8509c001f62f860f97b05d1fd3f87220c8b832578e6a483c719ca272b73a77f2231cb26395fa865e1cab2fd4298ab67786b69e97b8d757b938f4fc1f
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^5.0.0":
  version: 5.0.0
  resolution: "escape-string-regexp@npm:5.0.0"
  checksum: 10c0/6366f474c6f37a802800a435232395e04e9885919873e382b157ab7e8f0feb8fed71497f84a6f6a81a49aab41815522f5839112bd38026d203aea0c91622df95
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10c0/ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"estree-util-attach-comments@npm:^2.0.0":
  version: 2.1.1
  resolution: "estree-util-attach-comments@npm:2.1.1"
  dependencies:
    "@types/estree": "npm:^1.0.0"
  checksum: 10c0/cdb5fdb5809b376ca4a96afbcd916c3570b4bbf5d0115b8a9e1e8a10885d8d9fb549df0a16c077abb42ee35fa33192b69714bac25d4f3c43a36092288c9a64fd
  languageName: node
  linkType: hard

"estree-util-build-jsx@npm:^2.0.0":
  version: 2.2.2
  resolution: "estree-util-build-jsx@npm:2.2.2"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    estree-util-is-identifier-name: "npm:^2.0.0"
    estree-walker: "npm:^3.0.0"
  checksum: 10c0/2cef6ad6747f51934eba0601c3477ba08c98331cfe616635e08dfc89d06b9bbd370c4d80e87fe7d42d82776fa7840868201f48491b0ef9c808039f15fe4667e1
  languageName: node
  linkType: hard

"estree-util-is-identifier-name@npm:^2.0.0":
  version: 2.1.0
  resolution: "estree-util-is-identifier-name@npm:2.1.0"
  checksum: 10c0/cc241a6998d30f4e8775ec34b042ef93e0085cd1bdf692a01f22e9b748f0866c76679475ff87935be1d8d5b1a7648be8cba366dc60866b372269f35feec756fe
  languageName: node
  linkType: hard

"estree-util-to-js@npm:^1.1.0":
  version: 1.2.0
  resolution: "estree-util-to-js@npm:1.2.0"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    astring: "npm:^1.8.0"
    source-map: "npm:^0.7.0"
  checksum: 10c0/ad9c99dc34b0510ab813b485251acbf0abd06361c07b13c08da5d1611c279bee02ec09f2c269ae30b8d2da587115fc1fad4fa9f2f5ba69e094e758a3a4de7069
  languageName: node
  linkType: hard

"estree-util-visit@npm:^1.0.0":
  version: 1.2.1
  resolution: "estree-util-visit@npm:1.2.1"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/unist": "npm:^2.0.0"
  checksum: 10c0/3c47086ab25947a889fca9f58a842e0d27edadcad24dc393fdd7c9ad3419fe05b3c63b6fc9d6c9d8f50d32bca615cd0a3fe8d0e6b300fb94f74c91210b55ea5d
  languageName: node
  linkType: hard

"estree-walker@npm:^3.0.0":
  version: 3.0.3
  resolution: "estree-walker@npm:3.0.3"
  dependencies:
    "@types/estree": "npm:^1.0.0"
  checksum: 10c0/c12e3c2b2642d2bcae7d5aa495c60fa2f299160946535763969a1c83fc74518ffa9c2cd3a8b69ac56aea547df6a8aac25f729a342992ef0bbac5f1c73e78995d
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"extend-shallow@npm:^2.0.1":
  version: 2.0.1
  resolution: "extend-shallow@npm:2.0.1"
  dependencies:
    is-extendable: "npm:^0.1.0"
  checksum: 10c0/ee1cb0a18c9faddb42d791b2d64867bd6cfd0f3affb711782eb6e894dd193e2934a7f529426aac7c8ddb31ac5d38000a00aa2caf08aa3dfc3e1c8ff6ba340bd9
  languageName: node
  linkType: hard

"extend@npm:^3.0.0":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 10c0/73bf6e27406e80aa3e85b0d1c4fd987261e628064e170ca781125c0b635a3dabad5e05adbf07595ea0cf1e6c5396cacb214af933da7cbaf24fe75ff14818e8f9
  languageName: node
  linkType: hard

"external-editor@npm:^3.1.0":
  version: 3.1.0
  resolution: "external-editor@npm:3.1.0"
  dependencies:
    chardet: "npm:^0.7.0"
    iconv-lite: "npm:^0.4.24"
    tmp: "npm:^0.0.33"
  checksum: 10c0/c98f1ba3efdfa3c561db4447ff366a6adb5c1e2581462522c56a18bf90dfe4da382f9cd1feee3e330108c3595a854b218272539f311ba1b3298f841eb0fbf339
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-safe-stringify@npm:^2.0.7":
  version: 2.1.1
  resolution: "fast-safe-stringify@npm:2.1.1"
  checksum: 10c0/d90ec1c963394919828872f21edaa3ad6f1dddd288d2bd4e977027afff09f5db40f94e39536d4646f7e01761d704d72d51dce5af1b93717f3489ef808f5f4e4d
  languageName: node
  linkType: hard

"fault@npm:^1.0.0":
  version: 1.0.4
  resolution: "fault@npm:1.0.4"
  dependencies:
    format: "npm:^0.2.0"
  checksum: 10c0/c86c11500c1b676787296f31ade8473adcc6784f118f07c1a9429730b6288d0412f96e069ce010aa57e4f65a9cccb5abee8868bbe3c5f10de63b20482c9baebd
  languageName: node
  linkType: hard

"fault@npm:^2.0.0":
  version: 2.0.1
  resolution: "fault@npm:2.0.1"
  dependencies:
    format: "npm:^0.2.0"
  checksum: 10c0/b80fbf1019b9ce8b08ee09ce86e02b028563e13a32ac3be34e42bfac00a97b96d8dee6d31e26578ffc16224eb6729e01ff1f97ddfeee00494f4f56c0aeed4bdd
  languageName: node
  linkType: hard

"fdir@npm:^6.4.3":
  version: 6.4.3
  resolution: "fdir@npm:6.4.3"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/d13c10120e9625adf21d8d80481586200759928c19405a816b77dd28eaeb80e7c59c5def3e2941508045eb06d34eb47fad865ccc8bf98e6ab988bb0ed160fb6f
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.4
  resolution: "fdir@npm:6.4.4"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/6ccc33be16945ee7bc841e1b4178c0b4cf18d3804894cb482aa514651c962a162f96da7ffc6ebfaf0df311689fb70091b04dd6caffe28d56b9ebdc0e7ccadfdd
  languageName: node
  linkType: hard

"filelist@npm:^1.0.4":
  version: 1.0.4
  resolution: "filelist@npm:1.0.4"
  dependencies:
    minimatch: "npm:^5.0.1"
  checksum: 10c0/426b1de3944a3d153b053f1c0ebfd02dccd0308a4f9e832ad220707a6d1f1b3c9784d6cadf6b2f68f09a57565f63ebc7bcdc913ccf8012d834f472c46e596f41
  languageName: node
  linkType: hard

"flexsearch@npm:0.7.43":
  version: 0.7.43
  resolution: "flexsearch@npm:0.7.43"
  checksum: 10c0/797dc474ed97750b8e85c118b1af63eb2709da5fc05defcb13e96515774f28743ccb2448b63f3b703cf1ca571928c006069503dacf7d177bc07b9ee15e1f85d0
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"format@npm:^0.2.0":
  version: 0.2.2
  resolution: "format@npm:0.2.2"
  checksum: 10c0/6032ba747541a43abf3e37b402b2f72ee08ebcb58bf84d816443dd228959837f1cddf1e8775b29fa27ff133f4bd146d041bfca5f9cf27f048edf3d493cf8fee6
  languageName: node
  linkType: hard

"fs-extra@npm:^11.1.1":
  version: 11.3.0
  resolution: "fs-extra@npm:11.3.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/5f95e996186ff45463059feb115a22fb048bdaf7e487ecee8a8646c78ed8fdca63630e3077d4c16ce677051f5e60d3355a06f3cd61f3ca43f48cc58822a44d0a
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fsevents@npm:2.3.2":
  version: 2.3.2
  resolution: "fsevents@npm:2.3.2"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/be78a3efa3e181cda3cf7a4637cb527bcebb0bd0ea0440105a3bb45b86f9245b307dc10a2507e8f4498a7d4ec349d1910f4d73e4d4495b16103106e07eee735b
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.2
  resolution: "fsevents@patch:fsevents@npm%3A2.3.2#optional!builtin<compat/fsevents>::version=2.3.2&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10c0/c6c7b60271931fa752aeb92f2b47e355eac1af3a2673f47c9589e8f8a41adc74d45551c1bc57b5e66a80609f10ffb72b6f575e4370d61cc3f7f3aaff01757cde
  languageName: node
  linkType: hard

"github-slugger@npm:^2.0.0":
  version: 2.0.0
  resolution: "github-slugger@npm:2.0.0"
  checksum: 10c0/21b912b6b1e48f1e5a50b2292b48df0ff6abeeb0691b161b3d93d84f4ae6b1acd6ae23702e914af7ea5d441c096453cf0f621b72d57893946618d21dd1a1c486
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"globals@npm:^15.14.0":
  version: 15.15.0
  resolution: "globals@npm:15.15.0"
  checksum: 10c0/f9ae80996392ca71316495a39bec88ac43ae3525a438b5626cd9d5ce9d5500d0a98a266409605f8cd7241c7acf57c354a48111ea02a767ba4f374b806d6861fe
  languageName: node
  linkType: hard

"globalyzer@npm:0.1.0":
  version: 0.1.0
  resolution: "globalyzer@npm:0.1.0"
  checksum: 10c0/e16e47a5835cbe8a021423d4c7fcd9f5f85815b4190a7f50c1fdb95fc559d72e4fb30be96f106c66a99413f36d72da0f8323d19d27f60a8feec9d936139ec5a8
  languageName: node
  linkType: hard

"globrex@npm:^0.1.2":
  version: 0.1.2
  resolution: "globrex@npm:0.1.2"
  checksum: 10c0/a54c029520cf58bda1d8884f72bd49b4cd74e977883268d931fd83bcbd1a9eb96d57c7dbd4ad80148fb9247467ebfb9b215630b2ed7563b2a8de02e1ff7f89d1
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"gray-matter@npm:4.0.3":
  version: 4.0.3
  resolution: "gray-matter@npm:4.0.3"
  dependencies:
    js-yaml: "npm:^3.13.1"
    kind-of: "npm:^6.0.2"
    section-matter: "npm:^1.0.0"
    strip-bom-string: "npm:^1.0.0"
  checksum: 10c0/e38489906dad4f162ca01e0dcbdbed96d1a53740cef446b9bf76d80bec66fa799af07776a18077aee642346c5e1365ed95e4c91854a12bf40ba0d4fb43a625a6
  languageName: node
  linkType: hard

"hachure-fill@npm:^0.5.2":
  version: 0.5.2
  resolution: "hachure-fill@npm:0.5.2"
  checksum: 10c0/307e3b6f9f2d3c11a82099c3f71eecbb9c440c00c1f896ac1732c23e6dbff16a92bb893d222b8b721b89cf11e58649ca60b4c24e5663f705f877cefd40153429
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"hast-util-from-html@npm:^2.0.3":
  version: 2.0.3
  resolution: "hast-util-from-html@npm:2.0.3"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    devlop: "npm:^1.1.0"
    hast-util-from-parse5: "npm:^8.0.0"
    parse5: "npm:^7.0.0"
    vfile: "npm:^6.0.0"
    vfile-message: "npm:^4.0.0"
  checksum: 10c0/993ef707c1a12474c8d4094fc9706a72826c660a7e308ea54c50ad893353d32e139b7cbc67510c2e82feac572b320e3b05aeb13d0f9c6302d61261f337b46764
  languageName: node
  linkType: hard

"hast-util-from-parse5@npm:^7.0.0":
  version: 7.1.2
  resolution: "hast-util-from-parse5@npm:7.1.2"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    "@types/unist": "npm:^2.0.0"
    hastscript: "npm:^7.0.0"
    property-information: "npm:^6.0.0"
    vfile: "npm:^5.0.0"
    vfile-location: "npm:^4.0.0"
    web-namespaces: "npm:^2.0.0"
  checksum: 10c0/c1002816d0235ff0a1e888d71c191d3ecfbaba510aaef86eec00edcba8803a3e0ad901bb0e5430a9d2aee2d52c31aabacae8282394dc519c333017a46c68d1c8
  languageName: node
  linkType: hard

"hast-util-from-parse5@npm:^8.0.0":
  version: 8.0.3
  resolution: "hast-util-from-parse5@npm:8.0.3"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/unist": "npm:^3.0.0"
    devlop: "npm:^1.0.0"
    hastscript: "npm:^9.0.0"
    property-information: "npm:^7.0.0"
    vfile: "npm:^6.0.0"
    vfile-location: "npm:^5.0.0"
    web-namespaces: "npm:^2.0.0"
  checksum: 10c0/40ace6c0ad43c26f721c7499fe408e639cde917b2350c9299635e6326559855896dae3c3ebf7440df54766b96c4276a7823e8f376a2b6a28b37b591f03412545
  languageName: node
  linkType: hard

"hast-util-heading-rank@npm:^2.1.1":
  version: 2.1.1
  resolution: "hast-util-heading-rank@npm:2.1.1"
  dependencies:
    "@types/hast": "npm:^2.0.0"
  checksum: 10c0/e1451ae71ea4b524aae1e772063dabb44c13d812de198d6a2841d5d5425c64414d0a81df745bde00f1393b4cbc6990b91b5614f4f895183120ee418fa50ed2c7
  languageName: node
  linkType: hard

"hast-util-is-element@npm:^3.0.0":
  version: 3.0.0
  resolution: "hast-util-is-element@npm:3.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
  checksum: 10c0/f5361e4c9859c587ca8eb0d8343492f3077ccaa0f58a44cd09f35d5038f94d65152288dcd0c19336ef2c9491ec4d4e45fde2176b05293437021570aa0bc3613b
  languageName: node
  linkType: hard

"hast-util-parse-selector@npm:^2.0.0":
  version: 2.2.5
  resolution: "hast-util-parse-selector@npm:2.2.5"
  checksum: 10c0/29b7ee77960ded6a99d30c287d922243071cc07b39f2006f203bd08ee54eb8f66bdaa86ef6527477c766e2382d520b60ee4e4087f189888c35d8bcc020173648
  languageName: node
  linkType: hard

"hast-util-parse-selector@npm:^3.0.0":
  version: 3.1.1
  resolution: "hast-util-parse-selector@npm:3.1.1"
  dependencies:
    "@types/hast": "npm:^2.0.0"
  checksum: 10c0/34ac1707a477fd9764e328087163f1f21857bdb0f8d425bf41f6def7baf840e50e4bca2eb03072e3da4e39856de28893c4b688dcba0cc305160d53afcece4df4
  languageName: node
  linkType: hard

"hast-util-parse-selector@npm:^4.0.0":
  version: 4.0.0
  resolution: "hast-util-parse-selector@npm:4.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
  checksum: 10c0/5e98168cb44470dc274aabf1a28317e4feb09b1eaf7a48bbaa8c1de1b43a89cd195cb1284e535698e658e3ec26ad91bc5e52c9563c36feb75abbc68aaf68fb9f
  languageName: node
  linkType: hard

"hast-util-raw@npm:^7.2.0":
  version: 7.2.3
  resolution: "hast-util-raw@npm:7.2.3"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    "@types/parse5": "npm:^6.0.0"
    hast-util-from-parse5: "npm:^7.0.0"
    hast-util-to-parse5: "npm:^7.0.0"
    html-void-elements: "npm:^2.0.0"
    parse5: "npm:^6.0.0"
    unist-util-position: "npm:^4.0.0"
    unist-util-visit: "npm:^4.0.0"
    vfile: "npm:^5.0.0"
    web-namespaces: "npm:^2.0.0"
    zwitch: "npm:^2.0.0"
  checksum: 10c0/c7bf994938cbc1acaaeb337f99773773b51ad77695b559c6352cba5c35b26610e6de2936b5086ef8bc53b436dd8032a3860e7357f28b6bb0365f751919745398
  languageName: node
  linkType: hard

"hast-util-to-estree@npm:^2.0.0":
  version: 2.3.3
  resolution: "hast-util-to-estree@npm:2.3.3"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^2.0.0"
    "@types/unist": "npm:^2.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    estree-util-attach-comments: "npm:^2.0.0"
    estree-util-is-identifier-name: "npm:^2.0.0"
    hast-util-whitespace: "npm:^2.0.0"
    mdast-util-mdx-expression: "npm:^1.0.0"
    mdast-util-mdxjs-esm: "npm:^1.0.0"
    property-information: "npm:^6.0.0"
    space-separated-tokens: "npm:^2.0.0"
    style-to-object: "npm:^0.4.1"
    unist-util-position: "npm:^4.0.0"
    zwitch: "npm:^2.0.0"
  checksum: 10c0/5947b5030a6d20c193f5ea576cc751507e0b30d00f91e40a5208ca3a7add03a3862795a83600c0fdadf19c8b051917c7904715fa7dd358f04603d67a36341c38
  languageName: node
  linkType: hard

"hast-util-to-html@npm:^9.0.5":
  version: 9.0.5
  resolution: "hast-util-to-html@npm:9.0.5"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/unist": "npm:^3.0.0"
    ccount: "npm:^2.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    hast-util-whitespace: "npm:^3.0.0"
    html-void-elements: "npm:^3.0.0"
    mdast-util-to-hast: "npm:^13.0.0"
    property-information: "npm:^7.0.0"
    space-separated-tokens: "npm:^2.0.0"
    stringify-entities: "npm:^4.0.0"
    zwitch: "npm:^2.0.4"
  checksum: 10c0/b7a08c30bab4371fc9b4a620965c40b270e5ae7a8e94cf885f43b21705179e28c8e43b39c72885d1647965fb3738654e6962eb8b58b0c2a84271655b4d748836
  languageName: node
  linkType: hard

"hast-util-to-parse5@npm:^7.0.0":
  version: 7.1.0
  resolution: "hast-util-to-parse5@npm:7.1.0"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    property-information: "npm:^6.0.0"
    space-separated-tokens: "npm:^2.0.0"
    web-namespaces: "npm:^2.0.0"
    zwitch: "npm:^2.0.0"
  checksum: 10c0/2a96302b8f25fa2d5b657a94bb20a3d9a1a81e66c2f81582a242c5634dd850e3bd95313a7471eef8282b597f2129551fef5a1631f4ce14c41aab646281b339a0
  languageName: node
  linkType: hard

"hast-util-whitespace@npm:^2.0.0":
  version: 2.0.1
  resolution: "hast-util-whitespace@npm:2.0.1"
  checksum: 10c0/dcf6ebab091c802ffa7bb3112305c7631c15adb6c07a258f5528aefbddf82b4e162c8310ef426c48dc1dc623982cc33920e6dde5a50015d307f2778dcf6c2487
  languageName: node
  linkType: hard

"hast-util-whitespace@npm:^3.0.0":
  version: 3.0.0
  resolution: "hast-util-whitespace@npm:3.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
  checksum: 10c0/b898bc9fe27884b272580d15260b6bbdabe239973a147e97fa98c45fa0ffec967a481aaa42291ec34fb56530dc2d484d473d7e2bae79f39c83f3762307edfea8
  languageName: node
  linkType: hard

"hastscript@npm:^6.0.0":
  version: 6.0.0
  resolution: "hastscript@npm:6.0.0"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    comma-separated-tokens: "npm:^1.0.0"
    hast-util-parse-selector: "npm:^2.0.0"
    property-information: "npm:^5.0.0"
    space-separated-tokens: "npm:^1.0.0"
  checksum: 10c0/f76d9cf373cb075c8523c8ad52709f09f7e02b7c9d3152b8d35c65c265b9f1878bed6023f215a7d16523921036d40a7da292cb6f4399af9b5eccac2a5a5eb330
  languageName: node
  linkType: hard

"hastscript@npm:^7.0.0":
  version: 7.2.0
  resolution: "hastscript@npm:7.2.0"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    hast-util-parse-selector: "npm:^3.0.0"
    property-information: "npm:^6.0.0"
    space-separated-tokens: "npm:^2.0.0"
  checksum: 10c0/579912b03ff4a5b19eb609df7403c6dba2505ef1a1e2bc47cbf467cbd7cffcd51df40e74d882de1ccdda40aaf18487f82619eb9cb9f2077cba778017e95e868e
  languageName: node
  linkType: hard

"hastscript@npm:^9.0.0":
  version: 9.0.1
  resolution: "hastscript@npm:9.0.1"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    hast-util-parse-selector: "npm:^4.0.0"
    property-information: "npm:^7.0.0"
    space-separated-tokens: "npm:^2.0.0"
  checksum: 10c0/18dc8064e5c3a7a2ae862978e626b97a254e1c8a67ee9d0c9f06d373bba155ed805fc5b5ce21b990fb7bc174624889e5e1ce1cade264f1b1d58b48f994bc85ce
  languageName: node
  linkType: hard

"highlight.js@npm:^10.4.1, highlight.js@npm:~10.7.0":
  version: 10.7.3
  resolution: "highlight.js@npm:10.7.3"
  checksum: 10c0/073837eaf816922427a9005c56c42ad8786473dc042332dfe7901aa065e92bc3d94ebf704975257526482066abb2c8677cc0326559bb8621e046c21c5991c434
  languageName: node
  linkType: hard

"highlightjs-vue@npm:^1.0.0":
  version: 1.0.0
  resolution: "highlightjs-vue@npm:1.0.0"
  checksum: 10c0/9be378c70b864ca5eee87b07859222e31c946a8ad176227e54f7006a498223974ebe19fcce6e38ad5eb3c1ed0e16a580c4edefdf2cb882b6dfab1c3866cc047a
  languageName: node
  linkType: hard

"html-entities@npm:^2.1.0, html-entities@npm:^2.5.2":
  version: 2.5.2
  resolution: "html-entities@npm:2.5.2"
  checksum: 10c0/f20ffb4326606245c439c231de40a7c560607f639bf40ffbfb36b4c70729fd95d7964209045f1a4e62fe17f2364cef3d6e49b02ea09016f207fde51c2211e481
  languageName: node
  linkType: hard

"html-tag-names@npm:^2.1.0":
  version: 2.1.0
  resolution: "html-tag-names@npm:2.1.0"
  checksum: 10c0/ba25585db72ea26489c299e8ff4f819dbcb7c10d44c8066a645950e69eb3a9059f33f780ee1881735ac0fc9ed12f4c9fc7bec4767d4fd85fb2ff302832fb664e
  languageName: node
  linkType: hard

"html-to-text@npm:^9.0.5":
  version: 9.0.5
  resolution: "html-to-text@npm:9.0.5"
  dependencies:
    "@selderee/plugin-htmlparser2": "npm:^0.11.0"
    deepmerge: "npm:^4.3.1"
    dom-serializer: "npm:^2.0.0"
    htmlparser2: "npm:^8.0.2"
    selderee: "npm:^0.11.0"
  checksum: 10c0/5d2c77b798cf88a81b1da2fc1ea1a3b3e2ff49fe5a3d812392f802fff18ec315cf0969bd7846ef2eb7df8c37f463bc63e8cbdcf84e42696c6f3e15dfa61cdf4f
  languageName: node
  linkType: hard

"html-void-elements@npm:^2.0.0":
  version: 2.0.1
  resolution: "html-void-elements@npm:2.0.1"
  checksum: 10c0/1079c9e9fdb3b6a2481f2a282098a0183f3d45bf2b9d76c7dfc1671ee1857d7bacdd04fd8c6e2418f5ff550c30cabf97a010fe31ec402d0c89189807b48e6d79
  languageName: node
  linkType: hard

"html-void-elements@npm:^3.0.0":
  version: 3.0.0
  resolution: "html-void-elements@npm:3.0.0"
  checksum: 10c0/a8b9ec5db23b7c8053876dad73a0336183e6162bf6d2677376d8b38d654fdc59ba74fdd12f8812688f7db6fad451210c91b300e472afc0909224e0a44c8610d2
  languageName: node
  linkType: hard

"htmlparser2@npm:^6.0.0":
  version: 6.1.0
  resolution: "htmlparser2@npm:6.1.0"
  dependencies:
    domelementtype: "npm:^2.0.1"
    domhandler: "npm:^4.0.0"
    domutils: "npm:^2.5.2"
    entities: "npm:^2.0.0"
  checksum: 10c0/3058499c95634f04dc66be8c2e0927cd86799413b2d6989d8ae542ca4dbf5fa948695d02c27d573acf44843af977aec6d9a7bdd0f6faa6b2d99e2a729b2a31b6
  languageName: node
  linkType: hard

"htmlparser2@npm:^8.0.2":
  version: 8.0.2
  resolution: "htmlparser2@npm:8.0.2"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.0.1"
    entities: "npm:^4.4.0"
  checksum: 10c0/609cca85886d0bf2c9a5db8c6926a89f3764596877492e2caa7a25a789af4065bc6ee2cdc81807fe6b1d03a87bf8a373b5a754528a4cc05146b713c20575aab4
  languageName: node
  linkType: hard

"htmr@npm:^1.0.2":
  version: 1.0.2
  resolution: "htmr@npm:1.0.2"
  dependencies:
    html-entities: "npm:^2.1.0"
    htmlparser2: "npm:^6.0.0"
  peerDependencies:
    react: ">=15.6.1"
  checksum: 10c0/cebb895c019ec56ae29e120b76f9111a72e20aede8a3dbfc1f250d93861c5b0d03a0268e71dff272ba3f9b81a21fc859bfe93af126b28f49e683cd5f971d43f2
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 10c0/ce1319b8a382eb3cbb4a37c19f6bfe14e5bb5be3d09079e885e8c513ab2d3cd9214902f8a31c9dc4e37022633ceabfc2d697405deeaf1b8f3552bb4ed996fdfc
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"http2-client@npm:^1.2.5":
  version: 1.3.5
  resolution: "http2-client@npm:1.3.5"
  checksum: 10c0/4974f10f5c8b5b7b9e23771190471d02690e9a22c22e028d84715b7ecdcda05017fc9e565476558da3bdf0ba642d24186a94818d0b9afee706ccf9874034be73
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"hyperdyperid@npm:^1.2.0":
  version: 1.2.0
  resolution: "hyperdyperid@npm:1.2.0"
  checksum: 10c0/885ba3177c7181d315a856ee9c0005ff8eb5dcb1ce9e9d61be70987895d934d84686c37c981cceeb53216d4c9c15c1cc25f1804e84cc6a74a16993c5d7fd0893
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6, iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10c0/c6886a24cc00f2a059767440ec1bc00d334a89f250db8e0f7feb4961c8727118457e27c495ba94d082e51d3baca378726cd110aaf7ded8b9bbfd6a44760cf1d4
  languageName: node
  linkType: hard

"immutable@npm:^5.0.2":
  version: 5.0.3
  resolution: "immutable@npm:5.0.3"
  checksum: 10c0/3269827789e1026cd25c2ea97f0b2c19be852ffd49eda1b674b20178f73d84fa8d945ad6f5ac5bc4545c2b4170af9f6e1f77129bc1cae7974a4bf9b04a9cdfb9
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"inline-style-parser@npm:0.1.1":
  version: 0.1.1
  resolution: "inline-style-parser@npm:0.1.1"
  checksum: 10c0/08832a533f51a1e17619f2eabf2f5ec5e956d6dcba1896351285c65df022c9420de61d73256e1dca8015a52abf96cc84ddc3b73b898b22de6589d3962b5e501b
  languageName: node
  linkType: hard

"internmap@npm:1 - 2":
  version: 2.0.3
  resolution: "internmap@npm:2.0.3"
  checksum: 10c0/8cedd57f07bbc22501516fbfc70447f0c6812871d471096fad9ea603516eacc2137b633633daf432c029712df0baefd793686388ddf5737e3ea15074b877f7ed
  languageName: node
  linkType: hard

"internmap@npm:^1.0.0":
  version: 1.0.1
  resolution: "internmap@npm:1.0.1"
  checksum: 10c0/60942be815ca19da643b6d4f23bd0bf4e8c97abbd080fb963fe67583b60bdfb3530448ad4486bae40810e92317bded9995cc31411218acc750d72cd4e8646eee
  languageName: node
  linkType: hard

"invariant@npm:^2.2.4":
  version: 2.2.4
  resolution: "invariant@npm:2.2.4"
  dependencies:
    loose-envify: "npm:^1.0.0"
  checksum: 10c0/5af133a917c0bcf65e84e7f23e779e7abc1cd49cb7fdc62d00d1de74b0d8c1b5ee74ac7766099fb3be1b05b26dfc67bab76a17030d2fe7ea2eef867434362dfc
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"is-absolute-url@npm:^4.0.0":
  version: 4.0.1
  resolution: "is-absolute-url@npm:4.0.1"
  checksum: 10c0/6f8f603945bd9f2c6031758bbc12352fc647bd5d807cad10d96cc6300fd0e15240cc091521a61db767e4ec0bacff257b4f1015fd5249c147bbb4a4497356c72e
  languageName: node
  linkType: hard

"is-alphabetical@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-alphabetical@npm:1.0.4"
  checksum: 10c0/1505b1de5a1fd74022c05fb21b0e683a8f5229366bac8dc4d34cf6935bcfd104d1125a5e6b083fb778847629f76e5bdac538de5367bdf2b927a1356164e23985
  languageName: node
  linkType: hard

"is-alphabetical@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-alphabetical@npm:2.0.1"
  checksum: 10c0/932367456f17237533fd1fc9fe179df77957271020b83ea31da50e5cc472d35ef6b5fb8147453274ffd251134472ce24eb6f8d8398d96dee98237cdb81a6c9a7
  languageName: node
  linkType: hard

"is-alphanumerical@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-alphanumerical@npm:1.0.4"
  dependencies:
    is-alphabetical: "npm:^1.0.0"
    is-decimal: "npm:^1.0.0"
  checksum: 10c0/d623abae7130a7015c6bf33d99151d4e7005572fd170b86568ff4de5ae86ac7096608b87dd4a1d4dbbd497e392b6396930ba76c9297a69455909cebb68005905
  languageName: node
  linkType: hard

"is-alphanumerical@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-alphanumerical@npm:2.0.1"
  dependencies:
    is-alphabetical: "npm:^2.0.0"
    is-decimal: "npm:^2.0.0"
  checksum: 10c0/4b35c42b18e40d41378293f82a3ecd9de77049b476f748db5697c297f686e1e05b072a6aaae2d16f54d2a57f85b00cbbe755c75f6d583d1c77d6657bd0feb5a2
  languageName: node
  linkType: hard

"is-buffer@npm:^2.0.0":
  version: 2.0.5
  resolution: "is-buffer@npm:2.0.5"
  checksum: 10c0/e603f6fced83cf94c53399cff3bda1a9f08e391b872b64a73793b0928be3e5f047f2bcece230edb7632eaea2acdbfcb56c23b33d8a20c820023b230f1485679a
  languageName: node
  linkType: hard

"is-decimal@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-decimal@npm:1.0.4"
  checksum: 10c0/a4ad53c4c5c4f5a12214e7053b10326711f6a71f0c63ba1314a77bd71df566b778e4ebd29f9fb6815f07a4dc50c3767fb19bd6fc9fa05e601410f1d64ffeac48
  languageName: node
  linkType: hard

"is-decimal@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-decimal@npm:2.0.1"
  checksum: 10c0/8085dd66f7d82f9de818fba48b9e9c0429cb4291824e6c5f2622e96b9680b54a07a624cfc663b24148b8e853c62a1c987cfe8b0b5a13f5156991afaf6736e334
  languageName: node
  linkType: hard

"is-extendable@npm:^0.1.0":
  version: 0.1.1
  resolution: "is-extendable@npm:0.1.1"
  checksum: 10c0/dd5ca3994a28e1740d1e25192e66eed128e0b2ff161a7ea348e87ae4f616554b486854de423877a2a2c171d5f7cd6e8093b91f54533bc88a59ee1c9838c43879
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-hexadecimal@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-hexadecimal@npm:1.0.4"
  checksum: 10c0/ec4c64e5624c0f240922324bc697e166554f09d3ddc7633fc526084502626445d0a871fbd8cae52a9844e83bd0bb414193cc5a66806d7b2867907003fc70c5ea
  languageName: node
  linkType: hard

"is-hexadecimal@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-hexadecimal@npm:2.0.1"
  checksum: 10c0/3eb60fe2f1e2bbc760b927dcad4d51eaa0c60138cf7fc671803f66353ad90c301605b502c7ea4c6bb0548e1c7e79dfd37b73b632652e3b76030bba603a7e9626
  languageName: node
  linkType: hard

"is-plain-obj@npm:^4.0.0":
  version: 4.1.0
  resolution: "is-plain-obj@npm:4.1.0"
  checksum: 10c0/32130d651d71d9564dc88ba7e6fda0e91a1010a3694648e9f4f47bb6080438140696d3e3e15c741411d712e47ac9edc1a8a9de1fe76f3487b0d90be06ac9975e
  languageName: node
  linkType: hard

"is-reference@npm:^3.0.0":
  version: 3.0.3
  resolution: "is-reference@npm:3.0.3"
  dependencies:
    "@types/estree": "npm:^1.0.6"
  checksum: 10c0/35edd284cfb4cd9e9f08973f20e276ec517eaca31f5f049598e97dbb2d05544973dde212dac30fddee5b420930bff365e2e67dcd1293d0866c6720377382e3e5
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"isomorphic-rslog@npm:0.0.6":
  version: 0.0.6
  resolution: "isomorphic-rslog@npm:0.0.6"
  checksum: 10c0/ff702859d804ca13d5ed9f7de1d09a2bcfdb8e1dc8712713c569ea1833ecde1dcd1443057234d61ded22466f1775a2a94c6659d358678343f1efff0b5869e048
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jake@npm:^10.8.5":
  version: 10.9.2
  resolution: "jake@npm:10.9.2"
  dependencies:
    async: "npm:^3.2.3"
    chalk: "npm:^4.0.2"
    filelist: "npm:^1.0.4"
    minimatch: "npm:^3.1.2"
  bin:
    jake: bin/cli.js
  checksum: 10c0/c4597b5ed9b6a908252feab296485a4f87cba9e26d6c20e0ca144fb69e0c40203d34a2efddb33b3d297b8bd59605e6c1f44f6221ca1e10e69175ecbf3ff5fe31
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/6746baaaeac312c4db8e75fa22331d9a04cccb7792d126ed8ce6a0bbcfef0cedaddd0c5098fade53db067c09fe00aa1c957674b4765610a8b06a5a189e46433b
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"json5@npm:^2.1.2":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"katex@npm:^0.16.9":
  version: 0.16.21
  resolution: "katex@npm:0.16.21"
  dependencies:
    commander: "npm:^8.3.0"
  bin:
    katex: cli.js
  checksum: 10c0/e2e4139ba72a13f2393308fbb2b4c5511611a19a40a6e39d956cf775e553af3517dbfd0a54477faaf401c923e4654e32296347846b8ff15dfa579f88ff8579bb
  languageName: node
  linkType: hard

"khroma@npm:^2.1.0":
  version: 2.1.0
  resolution: "khroma@npm:2.1.0"
  checksum: 10c0/634d98753ff5d2540491cafeb708fc98de0d43f4e6795256d5c8f6e3ad77de93049ea41433928fda3697adf7bbe6fe27351858f6d23b78f8b5775ef314c59891
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.0, kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 10c0/61cdff9623dabf3568b6445e93e31376bee1cdb93f8ba7033d86022c2a9b1791a1d9510e026e6465ebd701a6dd2f7b0808483ad8838341ac52f003f512e0b4c4
  languageName: node
  linkType: hard

"kleur@npm:^4.0.3":
  version: 4.1.5
  resolution: "kleur@npm:4.1.5"
  checksum: 10c0/e9de6cb49657b6fa70ba2d1448fd3d691a5c4370d8f7bbf1c2f64c24d461270f2117e1b0afe8cb3114f13bbd8e51de158c2a224953960331904e636a5e4c0f2a
  languageName: node
  linkType: hard

"kolorist@npm:^1.8.0":
  version: 1.8.0
  resolution: "kolorist@npm:1.8.0"
  checksum: 10c0/73075db44a692bf6c34a649f3b4b3aea4993b84f6b754cbf7a8577e7c7db44c0bad87752bd23b0ce533f49de2244ce2ce03b7b1b667a85ae170a94782cc50f9b
  languageName: node
  linkType: hard

"langium@npm:3.3.1":
  version: 3.3.1
  resolution: "langium@npm:3.3.1"
  dependencies:
    chevrotain: "npm:~11.0.3"
    chevrotain-allstar: "npm:~0.3.0"
    vscode-languageserver: "npm:~9.0.1"
    vscode-languageserver-textdocument: "npm:~1.0.11"
    vscode-uri: "npm:~3.0.8"
  checksum: 10c0/0c54803068addb0f7c16a57fdb2db2e5d4d9a21259d477c3c7d0587c2c2f65a313f9eeef3c95ac1c2e41cd11d4f2eaf620d2c03fe839a3350ffee59d2b4c7647
  languageName: node
  linkType: hard

"layout-base@npm:^1.0.0":
  version: 1.0.2
  resolution: "layout-base@npm:1.0.2"
  checksum: 10c0/2a55d0460fd9f6ed53d7e301b9eb3dea19bda03815d616a40665ce6dc75c1f4d62e1ca19a897da1cfaf6de1b91de59cd6f2f79ba1258f3d7fccc7d46ca7f3337
  languageName: node
  linkType: hard

"layout-base@npm:^2.0.0":
  version: 2.0.1
  resolution: "layout-base@npm:2.0.1"
  checksum: 10c0/a44df9ef3cbff9916a10f616635e22b5787c89fa62b2fec6f99e8e6ee512c7cebd22668ce32dab5a83c934ba0a309c51a678aa0b40d70853de6c357893c0a88b
  languageName: node
  linkType: hard

"leac@npm:^0.6.0":
  version: 0.6.0
  resolution: "leac@npm:0.6.0"
  checksum: 10c0/5257781e10791ef8462eb1cbe5e48e3cda7692486f2a775265d6f5216cc088960c62f138163b8df0dcf2119d18673bfe7b050d6b41543d92a7b7ac90e4eb1e8b
  languageName: node
  linkType: hard

"loader-utils@npm:^2.0.4":
  version: 2.0.4
  resolution: "loader-utils@npm:2.0.4"
  dependencies:
    big.js: "npm:^5.2.2"
    emojis-list: "npm:^3.0.0"
    json5: "npm:^2.1.2"
  checksum: 10c0/d5654a77f9d339ec2a03d88221a5a695f337bf71eb8dea031b3223420bb818964ba8ed0069145c19b095f6c8b8fd386e602a3fc7ca987042bd8bb1dcc90d7100
  languageName: node
  linkType: hard

"local-pkg@npm:^1.0.0":
  version: 1.0.0
  resolution: "local-pkg@npm:1.0.0"
  dependencies:
    mlly: "npm:^1.7.3"
    pkg-types: "npm:^1.3.0"
  checksum: 10c0/7dec42760087425183d2f95f76fc6427922b1d6ad83493a58bb66a29865fd740cfc93be0cbf5871ec8d98c8681af375dbd3f076fc2c0655bab87789c9df6d5b7
  languageName: node
  linkType: hard

"lodash-es@npm:4.17.21, lodash-es@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 10c0/fb407355f7e6cd523a9383e76e6b455321f0f153a6c9625e21a8827d10c54c2a2341bd2ae8d034358b60e07325e1330c14c224ff582d04612a46a4f0479ff2f2
  languageName: node
  linkType: hard

"lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"longest-streak@npm:^3.0.0":
  version: 3.1.0
  resolution: "longest-streak@npm:3.1.0"
  checksum: 10c0/7c2f02d0454b52834d1bcedef79c557bd295ee71fdabb02d041ff3aa9da48a90b5df7c0409156dedbc4df9b65da18742652aaea4759d6ece01f08971af6a7eaa
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0, loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10c0/655d110220983c1a4b9c0c679a2e8016d4b67f6e9c7b5435ff5979ecdb20d0813f4dec0a08674fcbdd4846a3f07edbb50a36811fd37930b94aaa0d9daceb017e
  languageName: node
  linkType: hard

"lowlight@npm:^1.17.0":
  version: 1.20.0
  resolution: "lowlight@npm:1.20.0"
  dependencies:
    fault: "npm:^1.0.0"
    highlight.js: "npm:~10.7.0"
  checksum: 10c0/728bce6f6fe8b157f48d3324e597f452ce0eed2ccff1c0f41a9047380f944e971eb45bceb31f08fbb64d8f338dabb166f10049b35b92c7ec5cf0241d6adb3dea
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"markdown-extensions@npm:^1.0.0":
  version: 1.1.1
  resolution: "markdown-extensions@npm:1.1.1"
  checksum: 10c0/eb9154016502ad1fb4477683ddb5cae8ba3ca06451b381b04dc4c34e91d8d168129d50d404b717d6bf7d458e13088c109303fc72d57cee7151a6082b0e7bba71
  languageName: node
  linkType: hard

"markdown-table@npm:^3.0.0":
  version: 3.0.4
  resolution: "markdown-table@npm:3.0.4"
  checksum: 10c0/1257b31827629a54c24a5030a3dac952256c559174c95ce3ef89bebd6bff0cb1444b1fd667b1a1bb53307f83278111505b3e26f0c4e7b731e0060d435d2d930b
  languageName: node
  linkType: hard

"marked@npm:^15.0.7":
  version: 15.0.7
  resolution: "marked@npm:15.0.7"
  bin:
    marked: bin/marked.js
  checksum: 10c0/0b9d07bace37bbf0548bae356c4184765afa4d2296ed0be4418aa4bb0ce703f323dc1a475125d536581f9fe264797e6265dd0b57499d97c0fe0f29bc6d016343
  languageName: node
  linkType: hard

"mdast-util-definitions@npm:^5.0.0":
  version: 5.1.2
  resolution: "mdast-util-definitions@npm:5.1.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    "@types/unist": "npm:^2.0.0"
    unist-util-visit: "npm:^4.0.0"
  checksum: 10c0/da9049c15562e44ee4ea4a36113d98c6c9eaa3d8a17d6da2aef6a0626376dcd01d9ec007d77a8dfcad6d0cbd5c32a4abbad72a3f48c3172a55934c7d9a916480
  languageName: node
  linkType: hard

"mdast-util-directive@npm:^2.0.0":
  version: 2.2.4
  resolution: "mdast-util-directive@npm:2.2.4"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    "@types/unist": "npm:^2.0.0"
    mdast-util-from-markdown: "npm:^1.3.0"
    mdast-util-to-markdown: "npm:^1.5.0"
    parse-entities: "npm:^4.0.0"
    stringify-entities: "npm:^4.0.0"
    unist-util-visit-parents: "npm:^5.1.3"
  checksum: 10c0/ec426a547e83fdbc1e93d65e2fa1a39c2915ddacdf0685bbc9fa8b73a8761d3ef323e2d71bad4f0d0f3b1f994054d3521c8346072a59c1fa1aaeaa44342f3c80
  languageName: node
  linkType: hard

"mdast-util-find-and-replace@npm:^2.0.0":
  version: 2.2.2
  resolution: "mdast-util-find-and-replace@npm:2.2.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    escape-string-regexp: "npm:^5.0.0"
    unist-util-is: "npm:^5.0.0"
    unist-util-visit-parents: "npm:^5.0.0"
  checksum: 10c0/ce935f4bd4aeab47f91531a7f09dfab89aaeea62ad31029b43185c5b626921357703d8e5093c13073c097fdabfc57cb2f884d7dfad83dbe7239e351375d6797c
  languageName: node
  linkType: hard

"mdast-util-from-markdown@npm:^1.0.0, mdast-util-from-markdown@npm:^1.1.0, mdast-util-from-markdown@npm:^1.3.0":
  version: 1.3.1
  resolution: "mdast-util-from-markdown@npm:1.3.1"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    "@types/unist": "npm:^2.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    mdast-util-to-string: "npm:^3.1.0"
    micromark: "npm:^3.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^1.0.0"
    micromark-util-decode-string: "npm:^1.0.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/f4e901bf2a2e93fe35a339e0cff581efacce2f7117cd5652e9a270847bd7e2508b3e717b7b4156af54d4f896d63033e06ff9fafbf59a1d46fe17dd5e2a3f7846
  languageName: node
  linkType: hard

"mdast-util-frontmatter@npm:^1.0.0":
  version: 1.0.1
  resolution: "mdast-util-frontmatter@npm:1.0.1"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-to-markdown: "npm:^1.3.0"
    micromark-extension-frontmatter: "npm:^1.0.0"
  checksum: 10c0/53d5c66f1f1ce3a8aa0732e52c9b8bc8b136ae8ca4cc0d945d543ced39d0e322cb1343710600ec94283cb066a026ddf2407d1dca911cc34be001b2c88336339e
  languageName: node
  linkType: hard

"mdast-util-gfm-autolink-literal@npm:^1.0.0":
  version: 1.0.3
  resolution: "mdast-util-gfm-autolink-literal@npm:1.0.3"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    ccount: "npm:^2.0.0"
    mdast-util-find-and-replace: "npm:^2.0.0"
    micromark-util-character: "npm:^1.0.0"
  checksum: 10c0/750e312eae73c3f2e8aa0e8c5232cb1b905357ff37ac236927f1af50cdbee7c2cfe2379b148ac32fa4137eeb3b24601e1bb6135084af926c7cd808867804193f
  languageName: node
  linkType: hard

"mdast-util-gfm-footnote@npm:^1.0.0":
  version: 1.0.2
  resolution: "mdast-util-gfm-footnote@npm:1.0.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-to-markdown: "npm:^1.3.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
  checksum: 10c0/767973e46b9e2ae44e80e51a5e38ad0b032fc7f06a1a3095aa96c2886ba333941c764474a56b82e7db05efc56242a4789bc7fbbcc753d61512750e86a4192fe8
  languageName: node
  linkType: hard

"mdast-util-gfm-strikethrough@npm:^1.0.0":
  version: 1.0.3
  resolution: "mdast-util-gfm-strikethrough@npm:1.0.3"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-to-markdown: "npm:^1.3.0"
  checksum: 10c0/29616b3dfdd33d3cd13f9b3181a8562fa2fbacfcb04a37dba3c690ba6829f0231b145444de984726d9277b2bc90dd7d96fb9df9f6292d5e77d65a8659ee2f52b
  languageName: node
  linkType: hard

"mdast-util-gfm-table@npm:^1.0.0":
  version: 1.0.7
  resolution: "mdast-util-gfm-table@npm:1.0.7"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    markdown-table: "npm:^3.0.0"
    mdast-util-from-markdown: "npm:^1.0.0"
    mdast-util-to-markdown: "npm:^1.3.0"
  checksum: 10c0/a37a05a936292c4f48394123332d3c034a6e1b15bb3e7f3b94e6bce3260c9184fd388abbc4100827edd5485a6563098306994d15a729bde3c96de7a62ed5720b
  languageName: node
  linkType: hard

"mdast-util-gfm-task-list-item@npm:^1.0.0":
  version: 1.0.2
  resolution: "mdast-util-gfm-task-list-item@npm:1.0.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-to-markdown: "npm:^1.3.0"
  checksum: 10c0/91fa91f7d1a8797bf129008dab12d23917015ad12df00044e275b4459e8b383fbec6234338953a0089ef9c3a114d0a360c3e652eb0ebf6ece7e7a8fd3b5977c6
  languageName: node
  linkType: hard

"mdast-util-gfm@npm:^2.0.0":
  version: 2.0.2
  resolution: "mdast-util-gfm@npm:2.0.2"
  dependencies:
    mdast-util-from-markdown: "npm:^1.0.0"
    mdast-util-gfm-autolink-literal: "npm:^1.0.0"
    mdast-util-gfm-footnote: "npm:^1.0.0"
    mdast-util-gfm-strikethrough: "npm:^1.0.0"
    mdast-util-gfm-table: "npm:^1.0.0"
    mdast-util-gfm-task-list-item: "npm:^1.0.0"
    mdast-util-to-markdown: "npm:^1.0.0"
  checksum: 10c0/5b7f7f98a90a2962d7e0787e080c4e55b70119100c7685bbdb772d8d7865524aeffd1757edba5afba434250e0246b987c0617c2c635baaf51c26dbbb3b72dbec
  languageName: node
  linkType: hard

"mdast-util-mdx-expression@npm:^1.0.0":
  version: 1.3.2
  resolution: "mdast-util-mdx-expression@npm:1.3.2"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    mdast-util-from-markdown: "npm:^1.0.0"
    mdast-util-to-markdown: "npm:^1.0.0"
  checksum: 10c0/01f306ee809d28825cbec23b3c80376a0fbe69601b6b2843d23beb5662a31ec7560995f52b96b13093cc03de1130404a47f139d16f58c3f54e91e88f4bdd82d2
  languageName: node
  linkType: hard

"mdast-util-mdx-jsx@npm:^2.0.0":
  version: 2.1.4
  resolution: "mdast-util-mdx-jsx@npm:2.1.4"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    "@types/unist": "npm:^2.0.0"
    ccount: "npm:^2.0.0"
    mdast-util-from-markdown: "npm:^1.1.0"
    mdast-util-to-markdown: "npm:^1.3.0"
    parse-entities: "npm:^4.0.0"
    stringify-entities: "npm:^4.0.0"
    unist-util-remove-position: "npm:^4.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/b0c16e56a99c5167e60c98dbdbe82645549630fb529688642c4664ca5557ff0b3029c75146f5657cadb7908d5fa99810eacc5dcc51676d0877c8b4dcebb11cbe
  languageName: node
  linkType: hard

"mdast-util-mdx@npm:^2.0.0":
  version: 2.0.1
  resolution: "mdast-util-mdx@npm:2.0.1"
  dependencies:
    mdast-util-from-markdown: "npm:^1.0.0"
    mdast-util-mdx-expression: "npm:^1.0.0"
    mdast-util-mdx-jsx: "npm:^2.0.0"
    mdast-util-mdxjs-esm: "npm:^1.0.0"
    mdast-util-to-markdown: "npm:^1.0.0"
  checksum: 10c0/3b5e55781a7b7b4b7e71728a84afbec63516f251b3556efec52dbb4824c0733f5ebaa907d21211d008e5cb1a8265e6704bc062ee605f4c09e90fbfa2c6fbba3b
  languageName: node
  linkType: hard

"mdast-util-mdxjs-esm@npm:^1.0.0, mdast-util-mdxjs-esm@npm:^1.3.1":
  version: 1.3.1
  resolution: "mdast-util-mdxjs-esm@npm:1.3.1"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    mdast-util-from-markdown: "npm:^1.0.0"
    mdast-util-to-markdown: "npm:^1.0.0"
  checksum: 10c0/2ff0af34ea62004d39f15bd45b79e3008e68cae7e2510c9281e24a17e2c3f55d004524796166ef5aa3378798ca7f6c5f88883238f413577619bbaf41026b7e62
  languageName: node
  linkType: hard

"mdast-util-phrasing@npm:^3.0.0":
  version: 3.0.1
  resolution: "mdast-util-phrasing@npm:3.0.1"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    unist-util-is: "npm:^5.0.0"
  checksum: 10c0/5e00e303652a7581593549dbce20dfb69d687d79a972f7928f6ca1920ef5385bceb737a3d5292ab6d937ed8c67bb59771e80e88f530b78734fe7d155f833e32b
  languageName: node
  linkType: hard

"mdast-util-to-hast@npm:^12.1.0":
  version: 12.3.0
  resolution: "mdast-util-to-hast@npm:12.3.0"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    mdast-util-definitions: "npm:^5.0.0"
    micromark-util-sanitize-uri: "npm:^1.1.0"
    trim-lines: "npm:^3.0.0"
    unist-util-generated: "npm:^2.0.0"
    unist-util-position: "npm:^4.0.0"
    unist-util-visit: "npm:^4.0.0"
  checksum: 10c0/0753e45bfcce423f7a13979ac720a23ed8d6bafed174c387f43bbe8baf3838f3a043cd8006975b71e5c4068b7948f83f1348acea79801101af31eaec4e7a499a
  languageName: node
  linkType: hard

"mdast-util-to-hast@npm:^13.0.0":
  version: 13.2.0
  resolution: "mdast-util-to-hast@npm:13.2.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    "@ungap/structured-clone": "npm:^1.0.0"
    devlop: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^2.0.0"
    trim-lines: "npm:^3.0.0"
    unist-util-position: "npm:^5.0.0"
    unist-util-visit: "npm:^5.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10c0/9ee58def9287df8350cbb6f83ced90f9c088d72d4153780ad37854f87144cadc6f27b20347073b285173b1649b0723ddf0b9c78158608a804dcacb6bda6e1816
  languageName: node
  linkType: hard

"mdast-util-to-markdown@npm:^1.0.0, mdast-util-to-markdown@npm:^1.3.0, mdast-util-to-markdown@npm:^1.5.0":
  version: 1.5.0
  resolution: "mdast-util-to-markdown@npm:1.5.0"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    "@types/unist": "npm:^2.0.0"
    longest-streak: "npm:^3.0.0"
    mdast-util-phrasing: "npm:^3.0.0"
    mdast-util-to-string: "npm:^3.0.0"
    micromark-util-decode-string: "npm:^1.0.0"
    unist-util-visit: "npm:^4.0.0"
    zwitch: "npm:^2.0.0"
  checksum: 10c0/9831d14aa6c097750a90c7b87b4e814b040731c30606a794c9b136dc746633dd9ec07154ca97d4fec4eaf732cf89d14643424e2581732d6ee18c9b0e51ff7664
  languageName: node
  linkType: hard

"mdast-util-to-string@npm:^3.0.0, mdast-util-to-string@npm:^3.1.0":
  version: 3.2.0
  resolution: "mdast-util-to-string@npm:3.2.0"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
  checksum: 10c0/112f4bf0f6758dcb95deffdcf37afba7eaecdfe2ee13252de031723094d4d55220e147326690a8b91244758e2d678e7aeb1fdd0fa6ef3317c979bc42effd9a21
  languageName: node
  linkType: hard

"medium-zoom@npm:1.1.0":
  version: 1.1.0
  resolution: "medium-zoom@npm:1.1.0"
  checksum: 10c0/7d1f05e8eab045c33d7c04d4ee7bf04f5246cf7a720d7b5f5a51c36ab23666e363bcbb6bffae50b5948d5eb19361914cb0e26a1fce5c1fff7a266bc0217893f3
  languageName: node
  linkType: hard

"memfs@npm:^4.17.0":
  version: 4.17.0
  resolution: "memfs@npm:4.17.0"
  dependencies:
    "@jsonjoy.com/json-pack": "npm:^1.0.3"
    "@jsonjoy.com/util": "npm:^1.3.0"
    tree-dump: "npm:^1.0.1"
    tslib: "npm:^2.0.0"
  checksum: 10c0/2901f69e80e1fbefa8aafe994a253fff6f34eb176d8b80d57476311611e516a11ab4dd93f852c8739fe04f2b57d6a4ca7a1828fa0bd401ce631bcac214b3d58b
  languageName: node
  linkType: hard

"mermaid@npm:^11.6.0":
  version: 11.6.0
  resolution: "mermaid@npm:11.6.0"
  dependencies:
    "@braintree/sanitize-url": "npm:^7.0.4"
    "@iconify/utils": "npm:^2.1.33"
    "@mermaid-js/parser": "npm:^0.4.0"
    "@types/d3": "npm:^7.4.3"
    cytoscape: "npm:^3.29.3"
    cytoscape-cose-bilkent: "npm:^4.1.0"
    cytoscape-fcose: "npm:^2.2.0"
    d3: "npm:^7.9.0"
    d3-sankey: "npm:^0.12.3"
    dagre-d3-es: "npm:7.0.11"
    dayjs: "npm:^1.11.13"
    dompurify: "npm:^3.2.4"
    katex: "npm:^0.16.9"
    khroma: "npm:^2.1.0"
    lodash-es: "npm:^4.17.21"
    marked: "npm:^15.0.7"
    roughjs: "npm:^4.6.6"
    stylis: "npm:^4.3.6"
    ts-dedent: "npm:^2.2.0"
    uuid: "npm:^11.1.0"
  checksum: 10c0/69709ac58992ed532e1173e327b75f4135e226b7b9f61c15a759266a323b726ce429eef554357be1fc68463597a8111e9be4f7f013a6780b558e88ea3bda46b6
  languageName: node
  linkType: hard

"micromark-core-commonmark@npm:^1.0.0, micromark-core-commonmark@npm:^1.0.1":
  version: 1.1.0
  resolution: "micromark-core-commonmark@npm:1.1.0"
  dependencies:
    decode-named-character-reference: "npm:^1.0.0"
    micromark-factory-destination: "npm:^1.0.0"
    micromark-factory-label: "npm:^1.0.0"
    micromark-factory-space: "npm:^1.0.0"
    micromark-factory-title: "npm:^1.0.0"
    micromark-factory-whitespace: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-classify-character: "npm:^1.0.0"
    micromark-util-html-tag-name: "npm:^1.0.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
    micromark-util-resolve-all: "npm:^1.0.0"
    micromark-util-subtokenize: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.1"
    uvu: "npm:^0.5.0"
  checksum: 10c0/b3bf7b7004ce7dbb3ae151dcca4db1d12546f1b943affb2418da4b90b9ce59357373c433ee2eea4c868aee0791dafa355aeed19f5ef2b0acaf271f32f1ecbe6a
  languageName: node
  linkType: hard

"micromark-extension-directive@npm:^2.0.0":
  version: 2.2.1
  resolution: "micromark-extension-directive@npm:2.2.1"
  dependencies:
    micromark-factory-space: "npm:^1.0.0"
    micromark-factory-whitespace: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    parse-entities: "npm:^4.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/9034298c99e904d92aec154de03328016b9c6f76d5659fa866e6a409cf598af7a63ba08a6144c202950464220a13c63106674d4e6b74a75c033bd95620efffef
  languageName: node
  linkType: hard

"micromark-extension-frontmatter@npm:^1.0.0":
  version: 1.1.1
  resolution: "micromark-extension-frontmatter@npm:1.1.1"
  dependencies:
    fault: "npm:^2.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/b64e056f6f9eaef470491b2e7ebf70249dfad59d46700399aef24130bfa8eb943b65873ee0412fc10a274066309722be6bf86a779e54ac20cede6d2f05be5cdf
  languageName: node
  linkType: hard

"micromark-extension-gfm-autolink-literal@npm:^1.0.0":
  version: 1.0.5
  resolution: "micromark-extension-gfm-autolink-literal@npm:1.0.5"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/4964a52605ac36d24501d427e2d173fa39b5e0402275cb45068eba4898f4cb9cc57f7007b21b7514f0ab5f7b371b1701a5156a10b6ac8e77a7f36e830cf481d4
  languageName: node
  linkType: hard

"micromark-extension-gfm-footnote@npm:^1.0.0":
  version: 1.1.2
  resolution: "micromark-extension-gfm-footnote@npm:1.1.2"
  dependencies:
    micromark-core-commonmark: "npm:^1.0.0"
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/b8090876cc3da5436c6253b0b40e39ceaa470c2429f699c19ee4163cef3102c4cd16c4ac2ec8caf916037fad310cfb52a9ef182c75d50fca7419ba08faad9b39
  languageName: node
  linkType: hard

"micromark-extension-gfm-strikethrough@npm:^1.0.0":
  version: 1.0.7
  resolution: "micromark-extension-gfm-strikethrough@npm:1.0.7"
  dependencies:
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-classify-character: "npm:^1.0.0"
    micromark-util-resolve-all: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/b45fe93a7a412fc44bae7a183b92a988e17b49ed9d683bd80ee4dde96d462e1ca6b316dd64bda7759e4086d6d8686790a711e53c244f1f4d2b37e1cfe852884d
  languageName: node
  linkType: hard

"micromark-extension-gfm-table@npm:^1.0.0":
  version: 1.0.7
  resolution: "micromark-extension-gfm-table@npm:1.0.7"
  dependencies:
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/38b5af80ecab8206845a057338235bee6f47fb6cb904208be4b76e87906765821683e25bef85dfa485809f931eaf8cd55f16cd2f4d6e33b84f56edfaf1dfb129
  languageName: node
  linkType: hard

"micromark-extension-gfm-tagfilter@npm:^1.0.0":
  version: 1.0.2
  resolution: "micromark-extension-gfm-tagfilter@npm:1.0.2"
  dependencies:
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/7e1bf278255cf2a8d2dda9de84bc238b39c53100e25ba8d7168220d5b00dc74869a6cb038fbf2e76b8ae89efc66906762311797a906d7d9cdd71e07bfe1ed505
  languageName: node
  linkType: hard

"micromark-extension-gfm-task-list-item@npm:^1.0.0":
  version: 1.0.5
  resolution: "micromark-extension-gfm-task-list-item@npm:1.0.5"
  dependencies:
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/2179742fa2cbb243cc06bd9e43fbb94cd98e4814c9d368ddf8b4b5afa0348023f335626ae955e89d679e2c2662a7f82c315117a3b060c87bdb4420fee5a219d1
  languageName: node
  linkType: hard

"micromark-extension-gfm@npm:^2.0.0":
  version: 2.0.3
  resolution: "micromark-extension-gfm@npm:2.0.3"
  dependencies:
    micromark-extension-gfm-autolink-literal: "npm:^1.0.0"
    micromark-extension-gfm-footnote: "npm:^1.0.0"
    micromark-extension-gfm-strikethrough: "npm:^1.0.0"
    micromark-extension-gfm-table: "npm:^1.0.0"
    micromark-extension-gfm-tagfilter: "npm:^1.0.0"
    micromark-extension-gfm-task-list-item: "npm:^1.0.0"
    micromark-util-combine-extensions: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/53056376d14caf3fab2cc44881c1ad49d975776cc2267bca74abda2cb31f2a77ec0fb2bdb2dd97565f0d9943ad915ff192b89c1cee5d9d727569a5e38505799b
  languageName: node
  linkType: hard

"micromark-extension-mdx-expression@npm:^1.0.0":
  version: 1.0.8
  resolution: "micromark-extension-mdx-expression@npm:1.0.8"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    micromark-factory-mdx-expression: "npm:^1.0.0"
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-events-to-acorn: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/99e2997a54caafc4258979c0591b3fe8e31018079df833d559768092fec41e57a71225d423f4179cea4e8bc1af2f52f5c9ae640673619d8fe142ded875240da3
  languageName: node
  linkType: hard

"micromark-extension-mdx-jsx@npm:^1.0.0":
  version: 1.0.5
  resolution: "micromark-extension-mdx-jsx@npm:1.0.5"
  dependencies:
    "@types/acorn": "npm:^4.0.0"
    "@types/estree": "npm:^1.0.0"
    estree-util-is-identifier-name: "npm:^2.0.0"
    micromark-factory-mdx-expression: "npm:^1.0.0"
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/1b4bfbe60b9cabfabfb870f70ded8da0caacbaa3be6bdf07f6db25cc5a14c6bc970c34c60e5c80da1e97766064a117feb8160b6d661d69e530a4cc7ec97305de
  languageName: node
  linkType: hard

"micromark-extension-mdx-md@npm:^1.0.0":
  version: 1.0.1
  resolution: "micromark-extension-mdx-md@npm:1.0.1"
  dependencies:
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/9ad70b3a5e842fd7ebd93c8c48a32fd3d05fe77be06a08ef32462ea53e97d8f297e2c1c4b30a6929dbd05125279fe98bb04e9cc0bb686c691bdcf7d36c6e51b0
  languageName: node
  linkType: hard

"micromark-extension-mdxjs-esm@npm:^1.0.0":
  version: 1.0.5
  resolution: "micromark-extension-mdxjs-esm@npm:1.0.5"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    micromark-core-commonmark: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-events-to-acorn: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    unist-util-position-from-estree: "npm:^1.1.0"
    uvu: "npm:^0.5.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/612028bced78e882641a43c78fc4813a573b383dc0a7b90db75ed88b37bf5b5997dc7ead4a1011315b34f17bc76b7f4419de6ad9532a088102ab1eea0245d380
  languageName: node
  linkType: hard

"micromark-extension-mdxjs@npm:^1.0.0":
  version: 1.0.1
  resolution: "micromark-extension-mdxjs@npm:1.0.1"
  dependencies:
    acorn: "npm:^8.0.0"
    acorn-jsx: "npm:^5.0.0"
    micromark-extension-mdx-expression: "npm:^1.0.0"
    micromark-extension-mdx-jsx: "npm:^1.0.0"
    micromark-extension-mdx-md: "npm:^1.0.0"
    micromark-extension-mdxjs-esm: "npm:^1.0.0"
    micromark-util-combine-extensions: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/3f123e4afea9674c96934c9ea6a057ec9e5584992c50c36c173a2e331d272b1f4e2a8552364a0e2cb50703d0218831fdae1a17b563f0009aac6a35350e6a7b77
  languageName: node
  linkType: hard

"micromark-factory-destination@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-destination@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/71ebd9089bf0c9689b98ef42215c04032ae2701ae08c3546b663628553255dca18e5310dbdacddad3acd8de4f12a789835fff30dadc4da3c4e30387a75e6b488
  languageName: node
  linkType: hard

"micromark-factory-label@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-label@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/5e2cd2d8214bb92a34dfcedf9c7aecf565e3648650a3a6a0495ededf15f2318dd214dc069e3026402792cd5839d395313f8ef9c2e86ca34a8facaa0f75a77753
  languageName: node
  linkType: hard

"micromark-factory-mdx-expression@npm:^1.0.0":
  version: 1.0.9
  resolution: "micromark-factory-mdx-expression@npm:1.0.9"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-events-to-acorn: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    unist-util-position-from-estree: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/b28bd8e072f37ca91446fe8d113e4ae64baaef013b0cde4aa224add0ee40963ce3584b9709f7662d30491f875ae7104b897d37efa26cdaecf25082ed5bac7b8c
  languageName: node
  linkType: hard

"micromark-factory-space@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-space@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/3da81187ce003dd4178c7adc4674052fb8befc8f1a700ae4c8227755f38581a4ae963866dc4857488d62d1dc9837606c9f2f435fa1332f62a0f1c49b83c6a822
  languageName: node
  linkType: hard

"micromark-factory-title@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-title@npm:1.1.0"
  dependencies:
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/cf8c687d1d5c3928846a4791d4a7e2f1d7bdd2397051e20d60f06b7565a48bf85198ab6f85735e997ab3f0cbb80b8b6391f4f7ebc0aae2f2f8c3a08541257bf6
  languageName: node
  linkType: hard

"micromark-factory-whitespace@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-whitespace@npm:1.1.0"
  dependencies:
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/7248cc4534f9befb38c6f398b6e38efd3199f1428fc214c9cb7ed5b6e9fa7a82c0d8cdfa9bcacde62887c9a7c8c46baf5c318b2ae8f701afbccc8ad702e92dce
  languageName: node
  linkType: hard

"micromark-util-character@npm:^1.0.0":
  version: 1.2.0
  resolution: "micromark-util-character@npm:1.2.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/3390a675a50731b58a8e5493cd802e190427f10fa782079b455b00f6b54e406e36882df7d4a3bd32b709f7a2c3735b4912597ebc1c0a99566a8d8d0b816e2cd4
  languageName: node
  linkType: hard

"micromark-util-character@npm:^2.0.0":
  version: 2.1.1
  resolution: "micromark-util-character@npm:2.1.1"
  dependencies:
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/d3fe7a5e2c4060fc2a076f9ce699c82a2e87190a3946e1e5eea77f563869b504961f5668d9c9c014724db28ac32fa909070ea8b30c3a39bd0483cc6c04cc76a1
  languageName: node
  linkType: hard

"micromark-util-chunked@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-chunked@npm:1.1.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10c0/59534cf4aaf481ed58d65478d00eae0080df9b5816673f79b5ddb0cea263e5a9ee9cbb6cc565daf1eb3c8c4ff86fc4e25d38a0577539655cda823a4249efd358
  languageName: node
  linkType: hard

"micromark-util-classify-character@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-classify-character@npm:1.1.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/3266453dc0fdaf584e24c9b3c91d1ed180f76b5856699c51fd2549305814fcab7ec52afb4d3e83d002a9115cd2d2b2ffdc9c0b38ed85120822bf515cc00636ec
  languageName: node
  linkType: hard

"micromark-util-combine-extensions@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-combine-extensions@npm:1.1.0"
  dependencies:
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/0bc572fab3fe77f533c29aa1b75cb847b9fc9455f67a98623ef9740b925c0b0426ad9f09bbb56f1e844ea9ebada7873d1f06d27f7c979a917692b273c4b69e31
  languageName: node
  linkType: hard

"micromark-util-decode-numeric-character-reference@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-decode-numeric-character-reference@npm:1.1.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10c0/64ef2575e3fc2426976c19e16973348f20b59ddd5543f1467ac2e251f29e0a91f12089703d29ae985b0b9a408ee0d72f06d04ed3920811aa2402aabca3bdf9e4
  languageName: node
  linkType: hard

"micromark-util-decode-string@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-decode-string@npm:1.1.0"
  dependencies:
    decode-named-character-reference: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10c0/757a0aaa5ad6c50c7480bd75371d407ac75f5022cd4404aba07adadf1448189502aea9bb7b2d09d25e18745e0abf72b95506b6beb184bcccabe919e48e3a5df7
  languageName: node
  linkType: hard

"micromark-util-encode@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-encode@npm:1.1.0"
  checksum: 10c0/9878c9bc96999d45626a7597fffac85348ea842dce75d2417345cbf070a9941c62477bd0963bef37d4f0fd29f2982be6ddf416d62806f00ccb334af9d6ee87e7
  languageName: node
  linkType: hard

"micromark-util-encode@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-encode@npm:2.0.1"
  checksum: 10c0/b2b29f901093845da8a1bf997ea8b7f5e061ffdba85070dfe14b0197c48fda64ffcf82bfe53c90cf9dc185e69eef8c5d41cae3ba918b96bc279326921b59008a
  languageName: node
  linkType: hard

"micromark-util-events-to-acorn@npm:^1.0.0":
  version: 1.2.3
  resolution: "micromark-util-events-to-acorn@npm:1.2.3"
  dependencies:
    "@types/acorn": "npm:^4.0.0"
    "@types/estree": "npm:^1.0.0"
    "@types/unist": "npm:^2.0.0"
    estree-util-visit: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/cd3af7365806a0b22efb83cb7726cb835725c0bc22e04f7ea83f2f38a09e7132413eff6ab6d53652b969a7ec30e442731c3abbbe8a74dc2081c51fd10223c269
  languageName: node
  linkType: hard

"micromark-util-html-tag-name@npm:^1.0.0":
  version: 1.2.0
  resolution: "micromark-util-html-tag-name@npm:1.2.0"
  checksum: 10c0/15421869678d36b4fe51df453921e8186bff514a14e9f79f32b7e1cdd67874e22a66ad34a7f048dd132cbbbfc7c382ae2f777a2bfd1f245a47705dc1c6d4f199
  languageName: node
  linkType: hard

"micromark-util-normalize-identifier@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-normalize-identifier@npm:1.1.0"
  dependencies:
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10c0/a9657321a2392584e4d978061882117a84db7d2c2c1c052c0f5d25da089d463edb9f956d5beaf7f5768984b6f72d046d59b5972951ec7bf25397687a62b8278a
  languageName: node
  linkType: hard

"micromark-util-resolve-all@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-resolve-all@npm:1.1.0"
  dependencies:
    micromark-util-types: "npm:^1.0.0"
  checksum: 10c0/b5c95484c06e87bbbb60d8430eb030a458733a5270409f4c67892d1274737087ca6a7ca888987430e57cf1dcd44bb16390d3b3936a2bf07f7534ec8f52ce43c9
  languageName: node
  linkType: hard

"micromark-util-sanitize-uri@npm:^1.0.0, micromark-util-sanitize-uri@npm:^1.1.0":
  version: 1.2.0
  resolution: "micromark-util-sanitize-uri@npm:1.2.0"
  dependencies:
    micromark-util-character: "npm:^1.0.0"
    micromark-util-encode: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
  checksum: 10c0/dbdb98248e9f0408c7a00f1c1cd805775b41d213defd659533835f34b38da38e8f990bf7b3f782e96bffbc549aec9c3ecdab197d4ad5adbfe08f814a70327b6e
  languageName: node
  linkType: hard

"micromark-util-sanitize-uri@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-sanitize-uri@npm:2.0.1"
  dependencies:
    micromark-util-character: "npm:^2.0.0"
    micromark-util-encode: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10c0/60e92166e1870fd4f1961468c2651013ff760617342918e0e0c3c4e872433aa2e60c1e5a672bfe5d89dc98f742d6b33897585cf86ae002cda23e905a3c02527c
  languageName: node
  linkType: hard

"micromark-util-subtokenize@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-subtokenize@npm:1.1.0"
  dependencies:
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.0"
    uvu: "npm:^0.5.0"
  checksum: 10c0/f292b1b162845db50d36255c9d4c4c6d47931fbca3ac98a80c7e536d2163233fd662f8ca0479ee2b80f145c66a1394c7ed17dfce801439741211015e77e3901e
  languageName: node
  linkType: hard

"micromark-util-symbol@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-symbol@npm:1.1.0"
  checksum: 10c0/10ceaed33a90e6bfd3a5d57053dbb53f437d4809cc11430b5a09479c0ba601577059be9286df4a7eae6e350a60a2575dc9fa9d9872b5b8d058c875e075c33803
  languageName: node
  linkType: hard

"micromark-util-symbol@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-symbol@npm:2.0.1"
  checksum: 10c0/f2d1b207771e573232436618e78c5e46cd4b5c560dd4a6d63863d58018abbf49cb96ec69f7007471e51434c60de3c9268ef2bf46852f26ff4aacd10f9da16fe9
  languageName: node
  linkType: hard

"micromark-util-types@npm:^1.0.0, micromark-util-types@npm:^1.0.1":
  version: 1.1.0
  resolution: "micromark-util-types@npm:1.1.0"
  checksum: 10c0/a9749cb0a12a252ff536baabcb7012421b6fad4d91a5fdd80d7b33dc7b4c22e2d0c4637dfe5b902d00247fe6c9b01f4a24fce6b572b16ccaa4da90e6ce2a11e4
  languageName: node
  linkType: hard

"micromark-util-types@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-types@npm:2.0.1"
  checksum: 10c0/872ec9334bb42afcc91c5bed8b7ee03b75654b36c6f221ab4d2b1bb0299279f00db948bf38ec6bc1ec03d0cf7842c21ab805190bf676157ba587eb0386d38b71
  languageName: node
  linkType: hard

"micromark@npm:^3.0.0":
  version: 3.2.0
  resolution: "micromark@npm:3.2.0"
  dependencies:
    "@types/debug": "npm:^4.0.0"
    debug: "npm:^4.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    micromark-core-commonmark: "npm:^1.0.1"
    micromark-factory-space: "npm:^1.0.0"
    micromark-util-character: "npm:^1.0.0"
    micromark-util-chunked: "npm:^1.0.0"
    micromark-util-combine-extensions: "npm:^1.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^1.0.0"
    micromark-util-encode: "npm:^1.0.0"
    micromark-util-normalize-identifier: "npm:^1.0.0"
    micromark-util-resolve-all: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^1.0.0"
    micromark-util-subtokenize: "npm:^1.0.0"
    micromark-util-symbol: "npm:^1.0.0"
    micromark-util-types: "npm:^1.0.1"
    uvu: "npm:^0.5.0"
  checksum: 10c0/f243e805d1b3cc699fddae2de0b1492bc82462f1a709d7ae5c82039f88b1e009c959100184717e748be057b5f88603289d5681679a4e6fbabcd037beb34bc744
  languageName: node
  linkType: hard

"mime@npm:^3.0.0":
  version: 3.0.0
  resolution: "mime@npm:3.0.0"
  bin:
    mime: cli.js
  checksum: 10c0/402e792a8df1b2cc41cb77f0dcc46472b7944b7ec29cb5bbcd398624b6b97096728f1239766d3fdeb20551dd8d94738344c195a6ea10c4f906eb0356323b0531
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/3defdfd230914f22a8da203747c42ee3c405c39d4d37ffda284dac5e45b7e1f6c49aa8be606509002898e73091ff2a3bbfc59c2c6c71d4660609f63aa92f98e3
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
    rimraf: "npm:^5.0.5"
  checksum: 10c0/82f8bf70da8af656909a8ee299d7ed3b3372636749d29e105f97f20e88971be31f5ed7642f2e898f00283b68b701cc01307401cdc209b0efc5dd3818220e5093
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"mlly@npm:^1.7.3, mlly@npm:^1.7.4":
  version: 1.7.4
  resolution: "mlly@npm:1.7.4"
  dependencies:
    acorn: "npm:^8.14.0"
    pathe: "npm:^2.0.1"
    pkg-types: "npm:^1.3.0"
    ufo: "npm:^1.5.4"
  checksum: 10c0/69e738218a13d6365caf930e0ab4e2b848b84eec261597df9788cefb9930f3e40667be9cb58a4718834ba5f97a6efeef31d3b5a95f4388143fd4e0d0deff72ff
  languageName: node
  linkType: hard

"mri@npm:^1.1.0":
  version: 1.2.0
  resolution: "mri@npm:1.2.0"
  checksum: 10c0/a3d32379c2554cf7351db6237ddc18dc9e54e4214953f3da105b97dc3babe0deb3ffe99cf409b38ea47cc29f9430561ba6b53b24ab8f9ce97a4b50409e4a50e7
  languageName: node
  linkType: hard

"ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"mute-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "mute-stream@npm:2.0.0"
  checksum: 10c0/2cf48a2087175c60c8dcdbc619908b49c07f7adcfc37d29236b0c5c612d6204f789104c98cc44d38acab7b3c96f4a3ec2cfdc4934d0738d876dbefa2a12c69f4
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.8":
  version: 3.3.8
  resolution: "nanoid@npm:3.3.8"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/4b1bb29f6cfebf3be3bc4ad1f1296fb0a10a3043a79f34fbffe75d1621b4318319211cd420549459018ea3592f0d2f159247a6f874911d6d26eaaadda2478120
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"node-fetch-h2@npm:^2.3.0":
  version: 2.3.0
  resolution: "node-fetch-h2@npm:2.3.0"
  dependencies:
    http2-client: "npm:^1.2.5"
  checksum: 10c0/10f117c5aa1d475fff05028dddd617a61606083e4d6c4195dd5f5b03c973182e0d125e804771e6888d04f7d92b5c9c27a6149d1beedd6af1e0744f163e8a02d9
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.1":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.1.0
  resolution: "node-gyp@npm:11.1.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/c38977ce502f1ea41ba2b8721bd5b49bc3d5b3f813eabfac8414082faf0620ccb5211e15c4daecc23ed9f5e3e9cc4da00e575a0bcfc2a95a069294f2afa1e0cd
  languageName: node
  linkType: hard

"node-readfiles@npm:^0.2.0":
  version: 0.2.0
  resolution: "node-readfiles@npm:0.2.0"
  dependencies:
    es6-promise: "npm:^3.2.1"
  checksum: 10c0/9de2f741baae29f2422b22ef4399b5f7cb6c20372d4e88447a98d00a92cf1a35efdf942d24eee153a87d885aa7e7442b4bc6de33d4b91c47ba9da501780c76a1
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"nprogress@npm:^0.2.0":
  version: 0.2.0
  resolution: "nprogress@npm:0.2.0"
  checksum: 10c0/eab9a923a1ad1eed71a455ecfbc358442dd9bcd71b9fa3fa1c67eddf5159360b182c218f76fca320c97541a1b45e19ced04e6dcb044a662244c5419f8ae9e821
  languageName: node
  linkType: hard

"oas-kit-common@npm:^1.0.8":
  version: 1.0.8
  resolution: "oas-kit-common@npm:1.0.8"
  dependencies:
    fast-safe-stringify: "npm:^2.0.7"
  checksum: 10c0/5619a0bd19a07b52af1afeff26e44601002c0fd558d0020fdb720cb3723b60c83b80efede3a62110ce315f15b971751fb46396760e0e507fb8fd412cdc3808dd
  languageName: node
  linkType: hard

"oas-linter@npm:^3.2.2":
  version: 3.2.2
  resolution: "oas-linter@npm:3.2.2"
  dependencies:
    "@exodus/schemasafe": "npm:^1.0.0-rc.2"
    should: "npm:^13.2.1"
    yaml: "npm:^1.10.0"
  checksum: 10c0/5a8ea3d8a0bf185b676659d1e1c0b9b50695aeff53ccd5c264c8e99b4a7c0021f802b937913d76f0bc1a6a2b8ae151df764d95302b0829063b9b26f8c436871c
  languageName: node
  linkType: hard

"oas-resolver@npm:^2.5.6":
  version: 2.5.6
  resolution: "oas-resolver@npm:2.5.6"
  dependencies:
    node-fetch-h2: "npm:^2.3.0"
    oas-kit-common: "npm:^1.0.8"
    reftools: "npm:^1.1.9"
    yaml: "npm:^1.10.0"
    yargs: "npm:^17.0.1"
  bin:
    resolve: resolve.js
  checksum: 10c0/cfba5ba3f7ea6673a840836cf194a80ba7f77e6d1ee005aa35cc838cad56d7e455fa53753ae7cc38810c96405b8606e675098ea7023639cf546cb10343f180f9
  languageName: node
  linkType: hard

"oas-schema-walker@npm:^1.1.5":
  version: 1.1.5
  resolution: "oas-schema-walker@npm:1.1.5"
  checksum: 10c0/8ba6bd2a9a8ede2c5574f217653a9e2b889a7c5be69c664a57e293591c58952e8510f4f9e2a82fd5f52491c859ce5c2b68342e9b971e9667f6b811e7fb56fd54
  languageName: node
  linkType: hard

"oas-validator@npm:^5.0.8":
  version: 5.0.8
  resolution: "oas-validator@npm:5.0.8"
  dependencies:
    call-me-maybe: "npm:^1.0.1"
    oas-kit-common: "npm:^1.0.8"
    oas-linter: "npm:^3.2.2"
    oas-resolver: "npm:^2.5.6"
    oas-schema-walker: "npm:^1.1.5"
    reftools: "npm:^1.1.9"
    should: "npm:^13.2.1"
    yaml: "npm:^1.10.0"
  checksum: 10c0/16bb722042dcba93892c50db2201df6aeea9c3dd60e2f7bc18b36f23c610d136f52a5946908817f6fdd4139219fa4b177f952b9831039078b4c8730fa026b180
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"oniguruma-parser@npm:^0.12.1":
  version: 0.12.1
  resolution: "oniguruma-parser@npm:0.12.1"
  checksum: 10c0/b843ea54cda833efb19f856314afcbd43e903ece3de489ab78c527ddec84859208052557daa9fad4bdba89ebdd15b0cc250de86b3daf8c7cbe37bac5a6a185d3
  languageName: node
  linkType: hard

"oniguruma-to-es@npm:^4.3.3":
  version: 4.3.3
  resolution: "oniguruma-to-es@npm:4.3.3"
  dependencies:
    oniguruma-parser: "npm:^0.12.1"
    regex: "npm:^6.0.1"
    regex-recursion: "npm:^6.0.2"
  checksum: 10c0/bc034e84dfee4dbc061cf6364023e66e1667fb8dc3afcad3b7d6a2c77e2d4a4809396ee2fb8c1fd3d6f00f76f7ca14b773586bf862c5f0c0074c059e2a219252
  languageName: node
  linkType: hard

"openai@npm:^5.0.0-beta.0":
  version: 5.0.0-beta.0
  resolution: "openai@npm:5.0.0-beta.0"
  peerDependencies:
    ws: ^8.18.0
    zod: ^3.23.8
  peerDependenciesMeta:
    ws:
      optional: true
    zod:
      optional: true
  bin:
    openai: bin/cli
  checksum: 10c0/0b23ae6f159357ad10445f854471c527e14bf4c5dfd6f413b4863bd25e8f80ebcef9864f6f33a94036471906cb4c7e0cb4e8e035877a00c2a996d4e4a6ff6be0
  languageName: node
  linkType: hard

"openapi-types@npm:^12.1.3":
  version: 12.1.3
  resolution: "openapi-types@npm:12.1.3"
  checksum: 10c0/4ad4eb91ea834c237edfa6ab31394e87e00c888fc2918009763389c00d02342345195d6f302d61c3fd807f17723cd48df29b47b538b68375b3827b3758cd520f
  languageName: node
  linkType: hard

"openapi-typescript@npm:^5.4.1":
  version: 5.4.2
  resolution: "openapi-typescript@npm:5.4.2"
  dependencies:
    js-yaml: "npm:^4.1.0"
    mime: "npm:^3.0.0"
    prettier: "npm:^2.6.2"
    tiny-glob: "npm:^0.2.9"
    undici: "npm:^5.4.0"
    yargs-parser: "npm:^21.0.1"
  bin:
    openapi-typescript: bin/cli.js
  checksum: 10c0/41a9aa03f0fae710ef4b44d26d22ae01deaa653bfda55e57b02db6ce8bd783811e2d88791639c59d73c730cef4a1b4dede4e7d9dd895abfbff2669f60580d871
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 10c0/f438450224f8e2687605a8dd318f0db694b6293c5d835ae509a69e97c8de38b6994645337e5577f5001115470414638978cc49da1cdcc25106dad8738dc69990
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-ratelimit@npm:^1.0.1":
  version: 1.0.1
  resolution: "p-ratelimit@npm:1.0.1"
  checksum: 10c0/92be1f6ce8703c9ba04e82b2510d5fbb3db1799ffaad265737ba716128e77736c274bb9a43f7db95cd41a8a267212351f70c45dbbdb1324dbd364186291ab04b
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"package-manager-detector@npm:^0.2.8":
  version: 0.2.9
  resolution: "package-manager-detector@npm:0.2.9"
  checksum: 10c0/5fe1e80743fd110954f1904be4be32f34fc46c17b05e9e47a81e2f5777e474366cb570ed3b697a5ae8290860b53ac4b309898b24919dc1ddeb6d4097429113e1
  languageName: node
  linkType: hard

"pako@npm:^1.0.10, pako@npm:^1.0.11, pako@npm:^1.0.6":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 10c0/86dd99d8b34c3930345b8bbeb5e1cd8a05f608eeb40967b293f72fe469d0e9c88b783a8777e4cc7dc7c91ce54c5e93d88ff4b4f060e6ff18408fd21030d9ffbe
  languageName: node
  linkType: hard

"parse-entities@npm:^2.0.0":
  version: 2.0.0
  resolution: "parse-entities@npm:2.0.0"
  dependencies:
    character-entities: "npm:^1.0.0"
    character-entities-legacy: "npm:^1.0.0"
    character-reference-invalid: "npm:^1.0.0"
    is-alphanumerical: "npm:^1.0.0"
    is-decimal: "npm:^1.0.0"
    is-hexadecimal: "npm:^1.0.0"
  checksum: 10c0/f85a22c0ea406ff26b53fdc28641f01cc36fa49eb2e3135f02693286c89ef0bcefc2262d99b3688e20aac2a14fd10b75c518583e875c1b9fe3d1f937795e0854
  languageName: node
  linkType: hard

"parse-entities@npm:^4.0.0":
  version: 4.0.2
  resolution: "parse-entities@npm:4.0.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    character-entities-legacy: "npm:^3.0.0"
    character-reference-invalid: "npm:^2.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    is-alphanumerical: "npm:^2.0.0"
    is-decimal: "npm:^2.0.0"
    is-hexadecimal: "npm:^2.0.0"
  checksum: 10c0/a13906b1151750b78ed83d386294066daf5fb559e08c5af9591b2d98cc209123103016a01df776f65f8219ad26652d6d6b210d0974d452049cddfc53a8916c34
  languageName: node
  linkType: hard

"parse5@npm:^6.0.0":
  version: 6.0.1
  resolution: "parse5@npm:6.0.1"
  checksum: 10c0/595821edc094ecbcfb9ddcb46a3e1fe3a718540f8320eff08b8cf6742a5114cce2d46d45f95c26191c11b184dcaf4e2960abcd9c5ed9eb9393ac9a37efcfdecb
  languageName: node
  linkType: hard

"parse5@npm:^7.0.0":
  version: 7.2.1
  resolution: "parse5@npm:7.2.1"
  dependencies:
    entities: "npm:^4.5.0"
  checksum: 10c0/829d37a0c709215a887e410a7118d754f8e1afd7edb529db95bc7bbf8045fb0266a7b67801331d8e8d9d073ea75793624ec27ce9ff3b96862c3b9008f4d68e80
  languageName: node
  linkType: hard

"parseley@npm:^0.12.0":
  version: 0.12.1
  resolution: "parseley@npm:0.12.1"
  dependencies:
    leac: "npm:^0.6.0"
    peberminta: "npm:^0.9.0"
  checksum: 10c0/df3de74172b72305b867298a71e5882c413df75d30f2bafb5fb70779dfd349c5e4db03441fbf8ca83da8e4aa72bd0ef2b5c73086c4825d27d1c649d61bc0bcc0
  languageName: node
  linkType: hard

"path-data-parser@npm:0.1.0, path-data-parser@npm:^0.1.0":
  version: 0.1.0
  resolution: "path-data-parser@npm:0.1.0"
  checksum: 10c0/ba22d54669a8bc4a3df27431fe667900685585d1196085b803d0aa4066b83e709bbf2be7c1d2b56e706b49cc698231d55947c22abbfc4843ca424bbf8c985745
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"pathe@npm:^2.0.1":
  version: 2.0.3
  resolution: "pathe@npm:2.0.3"
  checksum: 10c0/c118dc5a8b5c4166011b2b70608762e260085180bb9e33e80a50dcdb1e78c010b1624f4280c492c92b05fc276715a4c357d1f9edc570f8f1b3d90b6839ebaca1
  languageName: node
  linkType: hard

"pdf-lib@npm:^1.17.1":
  version: 1.17.1
  resolution: "pdf-lib@npm:1.17.1"
  dependencies:
    "@pdf-lib/standard-fonts": "npm:^1.0.0"
    "@pdf-lib/upng": "npm:^1.0.1"
    pako: "npm:^1.0.11"
    tslib: "npm:^1.11.1"
  checksum: 10c0/a9489a402880dacd1389a3e14ff8f6139d58e3bc82f26b0fcbd0798b841aee6ccb7fcfab0992b574e57b40404ced0330a7170b3c6467363461a9df5d9daec489
  languageName: node
  linkType: hard

"pdf-merger-js@npm:^5.1.2":
  version: 5.1.2
  resolution: "pdf-merger-js@npm:5.1.2"
  dependencies:
    commander: "npm:^11.1.0"
    pdf-lib: "npm:^1.17.1"
  bin:
    pdf-merge: cli.js
  checksum: 10c0/b4d779e838ea9953109081992fc92879c8a5f172a93793e112380056de87d17513a98c334076a6ee7b8ac81175812bc5a943d4088ce1dc7fd2daac1041bc1745
  languageName: node
  linkType: hard

"peberminta@npm:^0.9.0":
  version: 0.9.0
  resolution: "peberminta@npm:0.9.0"
  checksum: 10c0/59c2c39269d9f7f559cf44582f1c0503524c6a9bc3478e0309adba2b41c71ab98745a239a4e6f98f46105291256e6d8f12ae9860d9f016b1c9a6f52c0b63bfe7
  languageName: node
  linkType: hard

"periscopic@npm:^3.0.0":
  version: 3.1.0
  resolution: "periscopic@npm:3.1.0"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    estree-walker: "npm:^3.0.0"
    is-reference: "npm:^3.0.0"
  checksum: 10c0/fb5ce7cd810c49254cdf1cd3892811e6dd1a1dfbdf5f10a0a33fb7141baac36443c4cad4f0e2b30abd4eac613f6ab845c2bc1b7ce66ae9694c7321e6ada5bd96
  languageName: node
  linkType: hard

"picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10c0/7c51f3ad2bb42c776f49ebf964c644958158be30d0a510efd5a395e8d49cb5acfed5b82c0c5b365523ce18e6ab85013c9ebe574f60305892ec3fa8eee8304ccc
  languageName: node
  linkType: hard

"pkg-types@npm:^1.3.0":
  version: 1.3.1
  resolution: "pkg-types@npm:1.3.1"
  dependencies:
    confbox: "npm:^0.1.8"
    mlly: "npm:^1.7.4"
    pathe: "npm:^2.0.1"
  checksum: 10c0/19e6cb8b66dcc66c89f2344aecfa47f2431c988cfa3366bdfdcfb1dd6695f87dcce37fbd90fe9d1605e2f4440b77f391e83c23255347c35cf84e7fd774d7fcea
  languageName: node
  linkType: hard

"playwright-core@npm:1.52.0":
  version: 1.52.0
  resolution: "playwright-core@npm:1.52.0"
  bin:
    playwright-core: cli.js
  checksum: 10c0/640945507e6ca2144e9f596b2a6ecac042c2fd3683ff99e6271e9a7b38f3602d415f282609d569456f66680aab8b3c5bb1b257d8fb63a7fc0ed648261110421f
  languageName: node
  linkType: hard

"playwright@npm:^1.52.0":
  version: 1.52.0
  resolution: "playwright@npm:1.52.0"
  dependencies:
    fsevents: "npm:2.3.2"
    playwright-core: "npm:1.52.0"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    playwright: cli.js
  checksum: 10c0/2c6edf1e15e59bbaf77f3fa0fe0ac975793c17cff835d9c8b8bc6395a3b6f1c01898b3058ab37891b2e4d424bcc8f1b4844fe70d943e0143d239d7451408c579
  languageName: node
  linkType: hard

"points-on-curve@npm:0.2.0, points-on-curve@npm:^0.2.0":
  version: 0.2.0
  resolution: "points-on-curve@npm:0.2.0"
  checksum: 10c0/f0d92343fcc2ad1f48334633e580574c1e0e28038a756133e171e537f270d6d64203feada5ee556e36f448a1b46e0306dee07b30f589f4e3ad720f6ee38ef48c
  languageName: node
  linkType: hard

"points-on-path@npm:^0.2.1":
  version: 0.2.1
  resolution: "points-on-path@npm:0.2.1"
  dependencies:
    path-data-parser: "npm:0.1.0"
    points-on-curve: "npm:0.2.0"
  checksum: 10c0/a7010340f9f196976f61838e767bb7b0b7f6273ab4fb9eb37c61001fe26fbfc3fcd63c96d5e85b9a4ab579213ab366f2ddaaf60e2a9253e2b91a62db33f395ba
  languageName: node
  linkType: hard

"postcss@npm:^8.5.2":
  version: 8.5.3
  resolution: "postcss@npm:8.5.3"
  dependencies:
    nanoid: "npm:^3.3.8"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/b75510d7b28c3ab728c8733dd01538314a18c52af426f199a3c9177e63eb08602a3938bfb66b62dc01350b9aed62087eabbf229af97a1659eb8d3513cec823b3
  languageName: node
  linkType: hard

"prettier@npm:^2.6.2":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: 10c0/463ea8f9a0946cd5b828d8cf27bd8b567345cf02f56562d5ecde198b91f47a76b7ac9eae0facd247ace70e927143af6135e8cf411986b8cb8478784a4d6d724a
  languageName: node
  linkType: hard

"prismjs@npm:^1.27.0":
  version: 1.29.0
  resolution: "prismjs@npm:1.29.0"
  checksum: 10c0/d906c4c4d01b446db549b4f57f72d5d7e6ccaca04ecc670fb85cea4d4b1acc1283e945a9cbc3d81819084a699b382f970e02f9d1378e14af9808d366d9ed7ec6
  languageName: node
  linkType: hard

"prismjs@npm:~1.27.0":
  version: 1.27.0
  resolution: "prismjs@npm:1.27.0"
  checksum: 10c0/841cbf53e837a42df9155c5ce1be52c4a0a8967ac916b52a27d066181a3578186c634e52d06d0547fb62b65c486b99b95f826dd54966619f9721b884f486b498
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"prop-types@npm:^15.0.0, prop-types@npm:^15.7.2":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10c0/59ece7ca2fb9838031d73a48d4becb9a7cc1ed10e610517c7d8f19a1e02fa47f7c27d557d8a5702bec3cfeccddc853579832b43f449e54635803f277b1c78077
  languageName: node
  linkType: hard

"property-information@npm:^5.0.0":
  version: 5.6.0
  resolution: "property-information@npm:5.6.0"
  dependencies:
    xtend: "npm:^4.0.0"
  checksum: 10c0/d54b77c31dc13bb6819559080b2c67d37d94be7dc271f404f139a16a57aa96fcc0b3ad806d4a5baef9e031744853e4afe3df2e37275aacb1f78079bbb652c5af
  languageName: node
  linkType: hard

"property-information@npm:^6.0.0":
  version: 6.5.0
  resolution: "property-information@npm:6.5.0"
  checksum: 10c0/981e0f9cc2e5acdb414a6fd48a99dd0fd3a4079e7a91ab41cf97a8534cf43e0e0bc1ffada6602a1b3d047a33db8b5fc2ef46d863507eda712d5ceedac443f0ef
  languageName: node
  linkType: hard

"property-information@npm:^7.0.0":
  version: 7.0.0
  resolution: "property-information@npm:7.0.0"
  checksum: 10c0/bf443e3bbdfc154da8f4ff4c85ed97c3d21f5e5f77cce84d2fd653c6dfb974a75ad61eafbccb2b8d2285942be35d763eaa99d51e29dccc28b40917d3f018107e
  languageName: node
  linkType: hard

"react-dom@npm:^18.3.1":
  version: 18.3.1
  resolution: "react-dom@npm:18.3.1"
  dependencies:
    loose-envify: "npm:^1.1.0"
    scheduler: "npm:^0.23.2"
  peerDependencies:
    react: ^18.3.1
  checksum: 10c0/a752496c1941f958f2e8ac56239172296fcddce1365ce45222d04a1947e0cc5547df3e8447f855a81d6d39f008d7c32eab43db3712077f09e3f67c4874973e85
  languageName: node
  linkType: hard

"react-fast-compare@npm:^3.2.0":
  version: 3.2.2
  resolution: "react-fast-compare@npm:3.2.2"
  checksum: 10c0/0bbd2f3eb41ab2ff7380daaa55105db698d965c396df73e6874831dbafec8c4b5b08ba36ff09df01526caa3c61595247e3269558c284e37646241cba2b90a367
  languageName: node
  linkType: hard

"react-helmet-async@npm:^1.3.0":
  version: 1.3.0
  resolution: "react-helmet-async@npm:1.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.12.5"
    invariant: "npm:^2.2.4"
    prop-types: "npm:^15.7.2"
    react-fast-compare: "npm:^3.2.0"
    shallowequal: "npm:^1.1.0"
  peerDependencies:
    react: ^16.6.0 || ^17.0.0 || ^18.0.0
    react-dom: ^16.6.0 || ^17.0.0 || ^18.0.0
  checksum: 10c0/8f3e6d26beff61d2ed18f7b41561df3e4d83a7582914c7196aa65158c7f3cce939276547d7a0b8987952d9d44131406df74efba02d1f8fa8a3940b49e6ced70b
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10c0/33977da7a5f1a287936a0c85639fec6ca74f4f15ef1e59a6bc20338fc73dc69555381e211f7a3529b8150a1f71e4225525b41b60b52965bda53ce7d47377ada1
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: 10c0/f2f1e60010c683479e74c63f96b09fb41603527cd131a9959e2aee1e5a8b0caf270b365e5ca77d4a6b18aae659b60a86150bb3979073528877029b35aecd2072
  languageName: node
  linkType: hard

"react-lazy-with-preload@npm:^2.2.1":
  version: 2.2.1
  resolution: "react-lazy-with-preload@npm:2.2.1"
  checksum: 10c0/57c36baedc24a2c8fa148681dc7c5123ac183542964063b4a0433856a06d4edead27deb893012a43f75905af51a479b4409e688b97be3ef70cffff926954f6d2
  languageName: node
  linkType: hard

"react-markdown@npm:^8.0.7":
  version: 8.0.7
  resolution: "react-markdown@npm:8.0.7"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    "@types/prop-types": "npm:^15.0.0"
    "@types/unist": "npm:^2.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    hast-util-whitespace: "npm:^2.0.0"
    prop-types: "npm:^15.0.0"
    property-information: "npm:^6.0.0"
    react-is: "npm:^18.0.0"
    remark-parse: "npm:^10.0.0"
    remark-rehype: "npm:^10.0.0"
    space-separated-tokens: "npm:^2.0.0"
    style-to-object: "npm:^0.4.0"
    unified: "npm:^10.0.0"
    unist-util-visit: "npm:^4.0.0"
    vfile: "npm:^5.0.0"
  peerDependencies:
    "@types/react": ">=16"
    react: ">=16"
  checksum: 10c0/016617fbd2f4c03c5ae017fe39e89202f2ff536b4921dc1a5f7283d4b9d5157f20797adda75a8c59a06787ad0bc8841e2e437915aec645ce528e0a04a6d450ac
  languageName: node
  linkType: hard

"react-refresh@npm:^0.16.0":
  version: 0.16.0
  resolution: "react-refresh@npm:0.16.0"
  checksum: 10c0/122525dbd7a44140757f46b8b93df6a349126e64b270809a8f082809662be5837a97310e56df2cfc7dac98b8adfaaafa570ec579c8b269c374e6928394307c68
  languageName: node
  linkType: hard

"react-router-dom@npm:^6.29.0":
  version: 6.29.0
  resolution: "react-router-dom@npm:6.29.0"
  dependencies:
    "@remix-run/router": "npm:1.22.0"
    react-router: "npm:6.29.0"
  peerDependencies:
    react: ">=16.8"
    react-dom: ">=16.8"
  checksum: 10c0/f89f922006b6ff896ba81d82088812e42ae56790ccb838e7041eebe0f7d36ac2a4eca56512a422da4249cca23f389f998e84cf8ff868d4a83defd72951b8fbf9
  languageName: node
  linkType: hard

"react-router@npm:6.29.0":
  version: 6.29.0
  resolution: "react-router@npm:6.29.0"
  dependencies:
    "@remix-run/router": "npm:1.22.0"
  peerDependencies:
    react: ">=16.8"
  checksum: 10c0/0ad27b34e2ccb6db68ef124cd4492ba86b5422ea3e2af01c9de95e372eb3a36fb4727b40488ebc90e5e0cea41bc655c53569a754713554a465ca9423aa233df8
  languageName: node
  linkType: hard

"react-syntax-highlighter@npm:^15.6.1":
  version: 15.6.1
  resolution: "react-syntax-highlighter@npm:15.6.1"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    highlight.js: "npm:^10.4.1"
    highlightjs-vue: "npm:^1.0.0"
    lowlight: "npm:^1.17.0"
    prismjs: "npm:^1.27.0"
    refractor: "npm:^3.6.0"
  peerDependencies:
    react: ">= 0.14.0"
  checksum: 10c0/4a4cf4695c45d7a6b25078970fb79ae5a85edeba5be0a2508766ee18e8aee1c0c4cdd97bf54f5055e4af671fe7e5e71348e81cafe09a0eb07a763ae876b7f073
  languageName: node
  linkType: hard

"react@npm:^18.3.1":
  version: 18.3.1
  resolution: "react@npm:18.3.1"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10c0/283e8c5efcf37802c9d1ce767f302dd569dd97a70d9bb8c7be79a789b9902451e0d16334b05d73299b20f048cbc3c7d288bbbde10b701fa194e2089c237dbea3
  languageName: node
  linkType: hard

"readdirp@npm:^4.0.1":
  version: 4.1.2
  resolution: "readdirp@npm:4.1.2"
  checksum: 10c0/60a14f7619dec48c9c850255cd523e2717001b0e179dc7037cfa0895da7b9e9ab07532d324bfb118d73a710887d1e35f79c495fa91582784493e085d18c72c62
  languageName: node
  linkType: hard

"reduce-configs@npm:^1.1.0":
  version: 1.1.0
  resolution: "reduce-configs@npm:1.1.0"
  checksum: 10c0/23c7a7c5f75f70f5b8afd23a042e8ce58a953acab4c581d91d4f6efaaf8c43e4e7ab09478601b42f624e92ebf9c7e433d06005d90599dc069833942813852e10
  languageName: node
  linkType: hard

"refractor@npm:^3.6.0":
  version: 3.6.0
  resolution: "refractor@npm:3.6.0"
  dependencies:
    hastscript: "npm:^6.0.0"
    parse-entities: "npm:^2.0.0"
    prismjs: "npm:~1.27.0"
  checksum: 10c0/63ab62393c8c2fd7108c2ea1eff721c0ad2a1a6eee60fdd1b47f4bb25cf298667dc97d041405b3e718b0817da12b37a86ed07ebee5bd2ca6405611f1bae456db
  languageName: node
  linkType: hard

"reftools@npm:^1.1.9":
  version: 1.1.9
  resolution: "reftools@npm:1.1.9"
  checksum: 10c0/4b44c9e75d6e5328b43b974de08776ee1718a0b48f24e033b2699f872cc9a698234a4aa0553b9e1a766b828aeb9834e4aa988410f0279e86179edb33b270da6c
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 10c0/1b16eb2c4bceb1665c89de70dcb64126a22bc8eb958feef3cd68fe11ac6d2a4899b5cd1b80b0774c7c03591dc57d16631a7f69d2daa2ec98100e2f29f7ec4cc4
  languageName: node
  linkType: hard

"regex-recursion@npm:^6.0.2":
  version: 6.0.2
  resolution: "regex-recursion@npm:6.0.2"
  dependencies:
    regex-utilities: "npm:^2.3.0"
  checksum: 10c0/68e8b6889680e904b75d7f26edaf70a1a4dc1087406bff53face4c2929d918fd77c72223843fe816ac8ed9964f96b4160650e8d5909e26a998c6e9de324dadb1
  languageName: node
  linkType: hard

"regex-utilities@npm:^2.3.0":
  version: 2.3.0
  resolution: "regex-utilities@npm:2.3.0"
  checksum: 10c0/78c550a80a0af75223244fff006743922591bd8f61d91fef7c86b9b56cf9bbf8ee5d7adb6d8991b5e304c57c90103fc4818cf1e357b11c6c669b782839bd7893
  languageName: node
  linkType: hard

"regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "regex@npm:6.0.1"
  dependencies:
    regex-utilities: "npm:^2.3.0"
  checksum: 10c0/687b3e063d4ca19b0de7c55c24353f868a0fb9ba21512692470d2fb412e3a410894dd5924c91ea49d8cb8fa865e36ec956e52436ae0a256bdc095ff136c30aba
  languageName: node
  linkType: hard

"rehype-external-links@npm:^3.0.0":
  version: 3.0.0
  resolution: "rehype-external-links@npm:3.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@ungap/structured-clone": "npm:^1.0.0"
    hast-util-is-element: "npm:^3.0.0"
    is-absolute-url: "npm:^4.0.0"
    space-separated-tokens: "npm:^2.0.0"
    unist-util-visit: "npm:^5.0.0"
  checksum: 10c0/486b5db73d8fe72611d62b4eb0b56ec71025ea32bba764ad54473f714ca627be75e057ac29243763f85a77c3810f31727ce3e03c975b3803c1c98643d038e9ae
  languageName: node
  linkType: hard

"rehype-raw@npm:^6.1.1":
  version: 6.1.1
  resolution: "rehype-raw@npm:6.1.1"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    hast-util-raw: "npm:^7.2.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/c68b460d313cad877e731d83770913417e4759b3d7a824ffc0e60a7a62cdd7e24c461ead9b081760005382dd4510330e3bb961370e58dfeed09732675037a1a9
  languageName: node
  linkType: hard

"remark-directive@npm:^2.0.1":
  version: 2.0.1
  resolution: "remark-directive@npm:2.0.1"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-directive: "npm:^2.0.0"
    micromark-extension-directive: "npm:^2.0.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/b968a753d9c9472f3da9c47ce61d5e77b2ddc2805e887255bb5be502d3471ff88a33730763ca586813cd9c1c54e31d7e971458340c7bb2503a3eda95f45df563
  languageName: node
  linkType: hard

"remark-frontmatter@npm:^4.0.1":
  version: 4.0.1
  resolution: "remark-frontmatter@npm:4.0.1"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-frontmatter: "npm:^1.0.0"
    micromark-extension-frontmatter: "npm:^1.0.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/ec8386ba0fae654a69abbb130dfb8837cadc6844edb9d98700175deb4091d2f2611db58ccaf6dab354f72727573b6838147fe879962b655a426ba75ef19a99db
  languageName: node
  linkType: hard

"remark-gfm@npm:3.0.1, remark-gfm@npm:^3.0.1":
  version: 3.0.1
  resolution: "remark-gfm@npm:3.0.1"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-gfm: "npm:^2.0.0"
    micromark-extension-gfm: "npm:^2.0.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/53c4e82204f82f81949a170efdeb49d3c45137b7bca06a7ff857a483aac1a44b55ef0de8fb1bbe4f1292f2a378058e2e42e644f2c61f3e0cdc3e56afa4ec2a2c
  languageName: node
  linkType: hard

"remark-mdx@npm:^2.0.0":
  version: 2.3.0
  resolution: "remark-mdx@npm:2.3.0"
  dependencies:
    mdast-util-mdx: "npm:^2.0.0"
    micromark-extension-mdxjs: "npm:^1.0.0"
  checksum: 10c0/2688bbf03094a9cd17cc86afb6cf0270e86ffc696a2fe25ccb1befb84eb0864d281388dc560b585e05e20f94a994c9fa88492430d2ba703a2fef6918bca4c36b
  languageName: node
  linkType: hard

"remark-parse@npm:^10.0.0":
  version: 10.0.2
  resolution: "remark-parse@npm:10.0.2"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-from-markdown: "npm:^1.0.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/30cb8f2790380b1c7370a1c66cda41f33a7dc196b9e440a00e2675037bca55aea868165a8204e0cdbacc27ef4a3bdb7d45879826bd6efa07d9fdf328cb67a332
  languageName: node
  linkType: hard

"remark-rehype@npm:^10.0.0":
  version: 10.1.0
  resolution: "remark-rehype@npm:10.1.0"
  dependencies:
    "@types/hast": "npm:^2.0.0"
    "@types/mdast": "npm:^3.0.0"
    mdast-util-to-hast: "npm:^12.1.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/803e658c9b51a9b53ee2ada42ff82e8e570444bb97c873e0d602c2d8dcb69a774fd22bd6f26643dfd5ab4c181059ea6c9fb9a99a2d7f9665f3f11bef1a1489bd
  languageName: node
  linkType: hard

"remark-stringify@npm:^10.0.0":
  version: 10.0.3
  resolution: "remark-stringify@npm:10.0.3"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    mdast-util-to-markdown: "npm:^1.0.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/682f26c66ce72e97a00bff75774b68f8008184dc1e4407039e186de896b5cd5d336aa65842bf131f4826a7415acdcd01d14ba59ff41b421a250c23fb187cda85
  languageName: node
  linkType: hard

"remark@npm:^14.0.3":
  version: 14.0.3
  resolution: "remark@npm:14.0.3"
  dependencies:
    "@types/mdast": "npm:^3.0.0"
    remark-parse: "npm:^10.0.0"
    remark-stringify: "npm:^10.0.0"
    unified: "npm:^10.0.0"
  checksum: 10c0/d4dbef29abdb62b01ae632d787bf0038af819df3e4308e037754ece8cb9c4d8ea52e8629174a47b5c46a7448cd22feba5dfea09b3ffdaa8c745b4c9bdd01b41d
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10c0/83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: "npm:^10.3.7"
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 10c0/7da4fd0e15118ee05b918359462cfa1e7fe4b1228c7765195a45b55576e8c15b95db513b8466ec89129666f4af45ad978a3057a02139afba1a63512a2d9644cc
  languageName: node
  linkType: hard

"robust-predicates@npm:^3.0.2":
  version: 3.0.2
  resolution: "robust-predicates@npm:3.0.2"
  checksum: 10c0/4ecd53649f1c2d49529c85518f2fa69ffb2f7a4453f7fd19c042421c7b4d76c3efb48bc1c740c8f7049346d7cb58cf08ee0c9adaae595cc23564d360adb1fde4
  languageName: node
  linkType: hard

"root-workspace-0b6124@workspace:.":
  version: 0.0.0-use.local
  resolution: "root-workspace-0b6124@workspace:."
  dependencies:
    "@alauda/doom": "npm:^0.22.2"
    typescript: "npm:^5.7.2"
  languageName: unknown
  linkType: soft

"roughjs@npm:^4.6.6":
  version: 4.6.6
  resolution: "roughjs@npm:4.6.6"
  dependencies:
    hachure-fill: "npm:^0.5.2"
    path-data-parser: "npm:^0.1.0"
    points-on-curve: "npm:^0.2.0"
    points-on-path: "npm:^0.2.1"
  checksum: 10c0/68c11bf4516aa014cef2fe52426a9bab237c2f500d13e1a4f13b523cb5723667bf2d92b9619325efdc5bc2a193588ff5af8d51683df17cfb8720e96fe2b92b0c
  languageName: node
  linkType: hard

"rspack-plugin-virtual-module@npm:0.1.13":
  version: 0.1.13
  resolution: "rspack-plugin-virtual-module@npm:0.1.13"
  dependencies:
    fs-extra: "npm:^11.1.1"
  checksum: 10c0/2d5633ed167332b38d401aecccfc9e142158e85599fde4ddf12058d5be53a06f80e05c1270c3ccc60e183c4b718779f2cbbd0d32eec4bd8bfb21c9462749df3d
  languageName: node
  linkType: hard

"rw@npm:1":
  version: 1.3.3
  resolution: "rw@npm:1.3.3"
  checksum: 10c0/b1e1ef37d1e79d9dc7050787866e30b6ddcb2625149276045c262c6b4d53075ddc35f387a856a8e76f0d0df59f4cd58fe24707e40797ebee66e542b840ed6a53
  languageName: node
  linkType: hard

"rxjs@npm:^7.4.0":
  version: 7.8.2
  resolution: "rxjs@npm:7.8.2"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: 10c0/1fcd33d2066ada98ba8f21fcbbcaee9f0b271de1d38dc7f4e256bfbc6ffcdde68c8bfb69093de7eeb46f24b1fb820620bf0223706cff26b4ab99a7ff7b2e2c45
  languageName: node
  linkType: hard

"sade@npm:^1.7.3":
  version: 1.8.1
  resolution: "sade@npm:1.8.1"
  dependencies:
    mri: "npm:^1.1.0"
  checksum: 10c0/da8a3a5d667ad5ce3bf6d4f054bbb9f711103e5df21003c5a5c1a8a77ce12b640ed4017dd423b13c2307ea7e645adee7c2ae3afe8051b9db16a6f6d3da3f90b1
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sass-embedded-android-arm64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-android-arm64@npm:1.85.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"sass-embedded-android-arm@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-android-arm@npm:1.85.1"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"sass-embedded-android-ia32@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-android-ia32@npm:1.85.1"
  conditions: os=android & cpu=ia32
  languageName: node
  linkType: hard

"sass-embedded-android-riscv64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-android-riscv64@npm:1.85.1"
  conditions: os=android & cpu=riscv64
  languageName: node
  linkType: hard

"sass-embedded-android-x64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-android-x64@npm:1.85.1"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"sass-embedded-darwin-arm64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-darwin-arm64@npm:1.85.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"sass-embedded-darwin-x64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-darwin-x64@npm:1.85.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"sass-embedded-linux-arm64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-linux-arm64@npm:1.85.1"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"sass-embedded-linux-arm@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-linux-arm@npm:1.85.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"sass-embedded-linux-ia32@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-linux-ia32@npm:1.85.1"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"sass-embedded-linux-musl-arm64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-linux-musl-arm64@npm:1.85.1"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"sass-embedded-linux-musl-arm@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-linux-musl-arm@npm:1.85.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"sass-embedded-linux-musl-ia32@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-linux-musl-ia32@npm:1.85.1"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"sass-embedded-linux-musl-riscv64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-linux-musl-riscv64@npm:1.85.1"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"sass-embedded-linux-musl-x64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-linux-musl-x64@npm:1.85.1"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"sass-embedded-linux-riscv64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-linux-riscv64@npm:1.85.1"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"sass-embedded-linux-x64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-linux-x64@npm:1.85.1"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"sass-embedded-win32-arm64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-win32-arm64@npm:1.85.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"sass-embedded-win32-ia32@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-win32-ia32@npm:1.85.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"sass-embedded-win32-x64@npm:1.85.1":
  version: 1.85.1
  resolution: "sass-embedded-win32-x64@npm:1.85.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"sass-embedded@npm:^1.85.0":
  version: 1.85.1
  resolution: "sass-embedded@npm:1.85.1"
  dependencies:
    "@bufbuild/protobuf": "npm:^2.0.0"
    buffer-builder: "npm:^0.2.0"
    colorjs.io: "npm:^0.5.0"
    immutable: "npm:^5.0.2"
    rxjs: "npm:^7.4.0"
    sass-embedded-android-arm: "npm:1.85.1"
    sass-embedded-android-arm64: "npm:1.85.1"
    sass-embedded-android-ia32: "npm:1.85.1"
    sass-embedded-android-riscv64: "npm:1.85.1"
    sass-embedded-android-x64: "npm:1.85.1"
    sass-embedded-darwin-arm64: "npm:1.85.1"
    sass-embedded-darwin-x64: "npm:1.85.1"
    sass-embedded-linux-arm: "npm:1.85.1"
    sass-embedded-linux-arm64: "npm:1.85.1"
    sass-embedded-linux-ia32: "npm:1.85.1"
    sass-embedded-linux-musl-arm: "npm:1.85.1"
    sass-embedded-linux-musl-arm64: "npm:1.85.1"
    sass-embedded-linux-musl-ia32: "npm:1.85.1"
    sass-embedded-linux-musl-riscv64: "npm:1.85.1"
    sass-embedded-linux-musl-x64: "npm:1.85.1"
    sass-embedded-linux-riscv64: "npm:1.85.1"
    sass-embedded-linux-x64: "npm:1.85.1"
    sass-embedded-win32-arm64: "npm:1.85.1"
    sass-embedded-win32-ia32: "npm:1.85.1"
    sass-embedded-win32-x64: "npm:1.85.1"
    supports-color: "npm:^8.1.1"
    sync-child-process: "npm:^1.0.2"
    varint: "npm:^6.0.0"
  dependenciesMeta:
    sass-embedded-android-arm:
      optional: true
    sass-embedded-android-arm64:
      optional: true
    sass-embedded-android-ia32:
      optional: true
    sass-embedded-android-riscv64:
      optional: true
    sass-embedded-android-x64:
      optional: true
    sass-embedded-darwin-arm64:
      optional: true
    sass-embedded-darwin-x64:
      optional: true
    sass-embedded-linux-arm:
      optional: true
    sass-embedded-linux-arm64:
      optional: true
    sass-embedded-linux-ia32:
      optional: true
    sass-embedded-linux-musl-arm:
      optional: true
    sass-embedded-linux-musl-arm64:
      optional: true
    sass-embedded-linux-musl-ia32:
      optional: true
    sass-embedded-linux-musl-riscv64:
      optional: true
    sass-embedded-linux-musl-x64:
      optional: true
    sass-embedded-linux-riscv64:
      optional: true
    sass-embedded-linux-x64:
      optional: true
    sass-embedded-win32-arm64:
      optional: true
    sass-embedded-win32-ia32:
      optional: true
    sass-embedded-win32-x64:
      optional: true
  bin:
    sass: dist/bin/sass.js
  checksum: 10c0/be087fd67cc2563d17fad6254ec512fc37849490bc131e227e00baacc9f40d19dec77efc0d03ae6acfc4b19b34c1625bcaeabc1af7f9d3b92eee03b37891dc8d
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.2":
  version: 0.23.2
  resolution: "scheduler@npm:0.23.2"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10c0/26383305e249651d4c58e6705d5f8425f153211aef95f15161c151f7b8de885f24751b377e4a0b3dd42cce09aad3f87a61dab7636859c0d89b7daf1a1e2a5c78
  languageName: node
  linkType: hard

"section-matter@npm:^1.0.0":
  version: 1.0.0
  resolution: "section-matter@npm:1.0.0"
  dependencies:
    extend-shallow: "npm:^2.0.1"
    kind-of: "npm:^6.0.0"
  checksum: 10c0/8007f91780adc5aaa781a848eaae50b0f680bbf4043b90cf8a96778195b8fab690c87fe7a989e02394ce69890e330811ec8dab22397d384673ce59f7d750641d
  languageName: node
  linkType: hard

"selderee@npm:^0.11.0":
  version: 0.11.0
  resolution: "selderee@npm:0.11.0"
  dependencies:
    parseley: "npm:^0.12.0"
  checksum: 10c0/c2ad8313a0dbf3c0b74752a8d03cfbc0931ae77a36679cdb64733eb732c1762f95a5174249bf7e8b8103874cb0e013a030f9c8b72f5d41e62f1d847d4a845d39
  languageName: node
  linkType: hard

"semver@npm:^7.3.5":
  version: 7.7.1
  resolution: "semver@npm:7.7.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/fd603a6fb9c399c6054015433051bdbe7b99a940a8fb44b85c2b524c4004b023d7928d47cb22154f8d054ea7ee8597f586605e05b52047f048278e4ac56ae958
  languageName: node
  linkType: hard

"shallowequal@npm:^1.1.0":
  version: 1.1.0
  resolution: "shallowequal@npm:1.1.0"
  checksum: 10c0/b926efb51cd0f47aa9bc061add788a4a650550bbe50647962113a4579b60af2abe7b62f9b02314acc6f97151d4cf87033a2b15fc20852fae306d1a095215396c
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"shiki@npm:^3.4.0":
  version: 3.4.0
  resolution: "shiki@npm:3.4.0"
  dependencies:
    "@shikijs/core": "npm:3.4.0"
    "@shikijs/engine-javascript": "npm:3.4.0"
    "@shikijs/engine-oniguruma": "npm:3.4.0"
    "@shikijs/langs": "npm:3.4.0"
    "@shikijs/themes": "npm:3.4.0"
    "@shikijs/types": "npm:3.4.0"
    "@shikijs/vscode-textmate": "npm:^10.0.2"
    "@types/hast": "npm:^3.0.4"
  checksum: 10c0/00a61c86f5ef676dc0e6917a09ec18e9b3bbafc892ff7c22278bf542e425487dd7d96f31b981aa17d278072a5be81f38703401a8e12f702d9bb3c81fdc65083c
  languageName: node
  linkType: hard

"should-equal@npm:^2.0.0":
  version: 2.0.0
  resolution: "should-equal@npm:2.0.0"
  dependencies:
    should-type: "npm:^1.4.0"
  checksum: 10c0/b375e1da2586671e2b9442ac5b700af508f56438af9923f69123b1fe4e02ccddc9a8a3eb803447a6df91e616cec236c41d6f28fdaa100467f9fdb81651089538
  languageName: node
  linkType: hard

"should-format@npm:^3.0.3":
  version: 3.0.3
  resolution: "should-format@npm:3.0.3"
  dependencies:
    should-type: "npm:^1.3.0"
    should-type-adaptors: "npm:^1.0.1"
  checksum: 10c0/ef2a31148d79a3fabd0dc6c1c1b10f90d9e071ad8e1f99452bd01e8aceaca62985b43974cf8103185fa1a3ade85947c6f664e44ca9af253afd1ce93c223bd8e4
  languageName: node
  linkType: hard

"should-type-adaptors@npm:^1.0.1":
  version: 1.1.0
  resolution: "should-type-adaptors@npm:1.1.0"
  dependencies:
    should-type: "npm:^1.3.0"
    should-util: "npm:^1.0.0"
  checksum: 10c0/cf127f8807f69ace9db04dbec3f274330a854405feef9821b5fa525748961da65747869cca36c813132b98757bd3e42d53541579cb16630ccf3c0dd9c0082320
  languageName: node
  linkType: hard

"should-type@npm:^1.3.0, should-type@npm:^1.4.0":
  version: 1.4.0
  resolution: "should-type@npm:1.4.0"
  checksum: 10c0/50cb50d776ee117b151068367c09ec12ac8e6f5fe2bd4d167413972813f06e930fe8624232a56c335846d3afcb784455f9a9690baa4350b3919bd001f0c4c94b
  languageName: node
  linkType: hard

"should-util@npm:^1.0.0":
  version: 1.0.1
  resolution: "should-util@npm:1.0.1"
  checksum: 10c0/1790719e05eae9edae86e44cbbad98529bd333df3f7cdfd63ea80acb6af718990e70abbc173aa9ccb93fff5ab6ee08d38412d707ff4003840be2256a278a61f3
  languageName: node
  linkType: hard

"should@npm:^13.2.1":
  version: 13.2.3
  resolution: "should@npm:13.2.3"
  dependencies:
    should-equal: "npm:^2.0.0"
    should-format: "npm:^3.0.3"
    should-type: "npm:^1.4.0"
    should-type-adaptors: "npm:^1.0.1"
    should-util: "npm:^1.0.0"
  checksum: 10c0/99581d8615f6fb27cd23c9f431cfacef58d118a90d0cccf58775b90631a47441397cfbdcbe6379e2718e9e60f293e3dfc0e87857f4b5a36fe962814e46ab05fa
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1, signal-exit@npm:^4.1.0":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-git@npm:^3.27.0":
  version: 3.27.0
  resolution: "simple-git@npm:3.27.0"
  dependencies:
    "@kwsites/file-exists": "npm:^1.1.1"
    "@kwsites/promise-deferred": "npm:^1.1.1"
    debug: "npm:^4.3.5"
  checksum: 10c0/ef56cabea585377d3e0ca30e4e93447f465d91f23eaf751693cc31f366b5f7636facf52ad5bcd598bfdf295fa60732e7a394303d378995b52e2d221d92e5f9f4
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/00c3271e233ccf1fb83a3dd2060b94cc37817e0f797a93c560b9a7a86c4a0ec2961fb31263bdd24a3c28945e24868b5f063cd98744171d9e942c513454b50ae5
  languageName: node
  linkType: hard

"source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10c0/7bda1fc4c197e3c6ff17de1b8b2c20e60af81b63a52cb32ec5a5d67a20a7d42651e2cb34ebe93833c5a2a084377e17455854fee3e21e7925c64a51b6a52b0faf
  languageName: node
  linkType: hard

"source-map@npm:^0.7.0":
  version: 0.7.4
  resolution: "source-map@npm:0.7.4"
  checksum: 10c0/dc0cf3768fe23c345ea8760487f8c97ef6fca8a73c83cd7c9bf2fde8bc2c34adb9c0824d6feb14bc4f9e37fb522e18af621543f1289038a66ac7586da29aa7dc
  languageName: node
  linkType: hard

"space-separated-tokens@npm:^1.0.0":
  version: 1.1.5
  resolution: "space-separated-tokens@npm:1.1.5"
  checksum: 10c0/3ee0a6905f89e1ffdfe474124b1ade9fe97276a377a0b01350bc079b6ec566eb5b219e26064cc5b7f3899c05bde51ffbc9154290b96eaf82916a1e2c2c13ead9
  languageName: node
  linkType: hard

"space-separated-tokens@npm:^2.0.0":
  version: 2.0.2
  resolution: "space-separated-tokens@npm:2.0.2"
  checksum: 10c0/6173e1d903dca41dcab6a2deed8b4caf61bd13b6d7af8374713500570aa929ff9414ae09a0519f4f8772df993300305a395d4871f35bc4ca72b6db57e1f30af8
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10c0/ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"stackframe@npm:^1.3.4":
  version: 1.3.4
  resolution: "stackframe@npm:1.3.4"
  checksum: 10c0/18410f7a1e0c5d211a4effa83bdbf24adbe8faa8c34db52e1cd3e89837518c592be60b60d8b7270ac53eeeb8b807cd11b399a41667f6c9abb41059c3ccc8a989
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"stringify-entities@npm:^4.0.0":
  version: 4.0.4
  resolution: "stringify-entities@npm:4.0.4"
  dependencies:
    character-entities-html4: "npm:^2.0.0"
    character-entities-legacy: "npm:^3.0.0"
  checksum: 10c0/537c7e656354192406bdd08157d759cd615724e9d0873602d2c9b2f6a5c0a8d0b1d73a0a08677848105c5eebac6db037b57c0b3a4ec86331117fa7319ed50448
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-bom-string@npm:^1.0.0":
  version: 1.0.0
  resolution: "strip-bom-string@npm:1.0.0"
  checksum: 10c0/5c5717e2643225aa6a6d659d34176ab2657037f1fe2423ac6fcdb488f135e14fef1022030e426d8b4d0989e09adbd5c3288d5d3b9c632abeefd2358dfc512bca
  languageName: node
  linkType: hard

"style-to-object@npm:^0.4.0, style-to-object@npm:^0.4.1":
  version: 0.4.4
  resolution: "style-to-object@npm:0.4.4"
  dependencies:
    inline-style-parser: "npm:0.1.1"
  checksum: 10c0/3a733080da66952881175b17d65f92985cf94c1ca358a92cf21b114b1260d49b94a404ed79476047fb95698d64c7e366ca7443f0225939e2fb34c38bbc9c7639
  languageName: node
  linkType: hard

"stylis@npm:^4.3.6":
  version: 4.3.6
  resolution: "stylis@npm:4.3.6"
  checksum: 10c0/e736d484983a34f7c65d362c67dc79b7bce388054b261c2b7b23d02eaaf280617033f65d44b1ea341854f4331a5074b885668ac8741f98c13a6cfd6443ae85d0
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-color@npm:^8.1.1":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/ea1d3c275dd604c974670f63943ed9bd83623edc102430c05adb8efc56ba492746b6e95386e7831b872ec3807fd89dd8eb43f735195f37b5ec343e4234cc7e89
  languageName: node
  linkType: hard

"swagger2openapi@npm:^7.0.8":
  version: 7.0.8
  resolution: "swagger2openapi@npm:7.0.8"
  dependencies:
    call-me-maybe: "npm:^1.0.1"
    node-fetch: "npm:^2.6.1"
    node-fetch-h2: "npm:^2.3.0"
    node-readfiles: "npm:^0.2.0"
    oas-kit-common: "npm:^1.0.8"
    oas-resolver: "npm:^2.5.6"
    oas-schema-walker: "npm:^1.1.5"
    oas-validator: "npm:^5.0.8"
    reftools: "npm:^1.1.9"
    yaml: "npm:^1.10.0"
    yargs: "npm:^17.0.1"
  bin:
    boast: boast.js
    oas-validate: oas-validate.js
    swagger2openapi: swagger2openapi.js
  checksum: 10c0/441a4d3a7d353f99395b14a0c8d6124be6390f2f8aa53336905e7314a7f80b66f5f2a40ac0dc2dbe2f7bc01f52a223a94f54a2ece345095fd3ad8ae8b03d688b
  languageName: node
  linkType: hard

"sync-child-process@npm:^1.0.2":
  version: 1.0.2
  resolution: "sync-child-process@npm:1.0.2"
  dependencies:
    sync-message-port: "npm:^1.0.0"
  checksum: 10c0/f73c87251346fba28da8ac5bc8ed4c9474504a5250ab4bd44582beae8e25c230e0a5b7b16076488fee1aed39a1865de5ed4cec19c6fa4efdbb1081c514615170
  languageName: node
  linkType: hard

"sync-message-port@npm:^1.0.0":
  version: 1.1.3
  resolution: "sync-message-port@npm:1.1.3"
  checksum: 10c0/d259b08ab6da284135ba45bc13724268688b469371259f5978b2905e2c79342032b9240093b2483e83cfeccfd3a5e8300978e67090385f9b6b38941fcce1aec4
  languageName: node
  linkType: hard

"tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 10c0/bc40e6efe1e554d075469cedaba69a30eeb373552aaf41caeaaa45bf56ffacc2674261b106245bd566b35d8f3329b52d838e851ee0a852120acae26e622925c9
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"thingies@npm:^1.20.0":
  version: 1.21.0
  resolution: "thingies@npm:1.21.0"
  peerDependencies:
    tslib: ^2
  checksum: 10c0/7570ee855aecb73185a672ecf3eb1c287a6512bf5476449388433b2d4debcf78100bc8bfd439b0edd38d2bc3bfb8341de5ce85b8557dec66d0f27b962c9a8bc1
  languageName: node
  linkType: hard

"tiny-glob@npm:^0.2.9":
  version: 0.2.9
  resolution: "tiny-glob@npm:0.2.9"
  dependencies:
    globalyzer: "npm:0.1.0"
    globrex: "npm:^0.1.2"
  checksum: 10c0/cbe072f0d213a1395d30aa94845a051d4af18fe8ffb79c8e99ac1787cd25df69083f17791a53997cb65f469f48950cb61426ccc0683cc9df170ac2430e883702
  languageName: node
  linkType: hard

"tinyexec@npm:^0.3.2":
  version: 0.3.2
  resolution: "tinyexec@npm:0.3.2"
  checksum: 10c0/3efbf791a911be0bf0821eab37a3445c2ba07acc1522b1fa84ae1e55f10425076f1290f680286345ed919549ad67527d07281f1c19d584df3b74326909eb1f90
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.10":
  version: 0.2.12
  resolution: "tinyglobby@npm:0.2.12"
  dependencies:
    fdir: "npm:^6.4.3"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/7c9be4fd3625630e262dcb19015302aad3b4ba7fc620f269313e688f2161ea8724d6cb4444baab5ef2826eb6bed72647b169a33ec8eea37501832a2526ff540f
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.13":
  version: 0.2.13
  resolution: "tinyglobby@npm:0.2.13"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/ef07dfaa7b26936601d3f6d999f7928a4d1c6234c5eb36896bb88681947c0d459b7ebe797022400e555fe4b894db06e922b95d0ce60cb05fd827a0a66326b18c
  languageName: node
  linkType: hard

"tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: "npm:~1.0.2"
  checksum: 10c0/69863947b8c29cabad43fe0ce65cec5bb4b481d15d4b4b21e036b060b3edbf3bc7a5541de1bacb437bb3f7c4538f669752627fdf9b4aaf034cebd172ba373408
  languageName: node
  linkType: hard

"toggle-selection@npm:^1.0.6":
  version: 1.0.6
  resolution: "toggle-selection@npm:1.0.6"
  checksum: 10c0/f2cf1f2c70f374fd87b0cdc8007453ba9e981c4305a8bf4eac10a30e62ecdfd28bca7d18f8f15b15a506bf8a7bfb20dbe3539f0fcf2a2c8396c1a78d53e1f179
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"tree-dump@npm:^1.0.1":
  version: 1.0.2
  resolution: "tree-dump@npm:1.0.2"
  peerDependencies:
    tslib: 2
  checksum: 10c0/d1d180764e9c691b28332dbd74226c6b6af361dfb1e134bb11e60e17cb11c215894adee50ffc578da5dcf546006693947be8b6665eb1269b56e2f534926f1c1f
  languageName: node
  linkType: hard

"trim-lines@npm:^3.0.0":
  version: 3.0.1
  resolution: "trim-lines@npm:3.0.1"
  checksum: 10c0/3a1611fa9e52aa56a94c69951a9ea15b8aaad760eaa26c56a65330dc8adf99cb282fc07cc9d94968b7d4d88003beba220a7278bbe2063328eb23fb56f9509e94
  languageName: node
  linkType: hard

"trough@npm:^2.0.0":
  version: 2.2.0
  resolution: "trough@npm:2.2.0"
  checksum: 10c0/58b671fc970e7867a48514168894396dd94e6d9d6456aca427cc299c004fe67f35ed7172a36449086b2edde10e78a71a284ec0076809add6834fb8f857ccb9b0
  languageName: node
  linkType: hard

"ts-dedent@npm:^2.2.0":
  version: 2.2.0
  resolution: "ts-dedent@npm:2.2.0"
  checksum: 10c0/175adea838468cc2ff7d5e97f970dcb798bbcb623f29c6088cb21aa2880d207c5784be81ab1741f56b9ac37840cbaba0c0d79f7f8b67ffe61c02634cafa5c303
  languageName: node
  linkType: hard

"tslib@npm:^1.11.1":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: 10c0/69ae09c49eea644bc5ebe1bca4fa4cc2c82b7b3e02f43b84bd891504edf66dbc6b2ec0eef31a957042de2269139e4acff911e6d186a258fb14069cd7f6febce2
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.1.0, tslib@npm:^2.8.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10c0/902bd57bfa30d51d4779b641c2bc403cdf1371fb9c91d3c058b0133694fcfdb817aef07a47f40faf79039eecbaa39ee9d3c532deff244f3a19ce68cea71a61e8
  languageName: node
  linkType: hard

"type-fest@npm:^4.41.0":
  version: 4.41.0
  resolution: "type-fest@npm:4.41.0"
  checksum: 10c0/f5ca697797ed5e88d33ac8f1fec21921839871f808dc59345c9cf67345bfb958ce41bd821165dbf3ae591cedec2bf6fe8882098dfdd8dc54320b859711a2c1e4
  languageName: node
  linkType: hard

"typescript@npm:^5.7.2":
  version: 5.7.3
  resolution: "typescript@npm:5.7.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/b7580d716cf1824736cc6e628ab4cd8b51877408ba2be0869d2866da35ef8366dd6ae9eb9d0851470a39be17cbd61df1126f9e211d8799d764ea7431d5435afa
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^5.7.2#optional!builtin<compat/typescript>":
  version: 5.7.3
  resolution: "typescript@patch:typescript@npm%3A5.7.3#optional!builtin<compat/typescript>::version=5.7.3&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/6fd7e0ed3bf23a81246878c613423730c40e8bdbfec4c6e4d7bf1b847cbb39076e56ad5f50aa9d7ebd89877999abaee216002d3f2818885e41c907caaa192cc4
  languageName: node
  linkType: hard

"ufo@npm:^1.5.4":
  version: 1.5.4
  resolution: "ufo@npm:1.5.4"
  checksum: 10c0/b5dc4dc435c49c9ef8890f1b280a19ee4d0954d1d6f9ab66ce62ce64dd04c7be476781531f952a07c678d51638d02ad4b98e16237be29149295b0f7c09cda765
  languageName: node
  linkType: hard

"undici-types@npm:~6.19.2":
  version: 6.19.8
  resolution: "undici-types@npm:6.19.8"
  checksum: 10c0/078afa5990fba110f6824823ace86073b4638f1d5112ee26e790155f481f2a868cc3e0615505b6f4282bdf74a3d8caad715fd809e870c2bb0704e3ea6082f344
  languageName: node
  linkType: hard

"undici@npm:^5.4.0":
  version: 5.28.5
  resolution: "undici@npm:5.28.5"
  dependencies:
    "@fastify/busboy": "npm:^2.0.0"
  checksum: 10c0/4dfaa13089fe4c0758f84ec0d34b257e58608e6be3aa540f493b9864b39e3fdcd0a1ace38e434fe79db55f833aa30bcfddd8d6cbe3e0982b0dcae8ec17b65e08
  languageName: node
  linkType: hard

"unified@npm:^10.0.0, unified@npm:^10.1.2":
  version: 10.1.2
  resolution: "unified@npm:10.1.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    bail: "npm:^2.0.0"
    extend: "npm:^3.0.0"
    is-buffer: "npm:^2.0.0"
    is-plain-obj: "npm:^4.0.0"
    trough: "npm:^2.0.0"
    vfile: "npm:^5.0.0"
  checksum: 10c0/da9195e3375a74ab861a65e1d7b0454225d17a61646697911eb6b3e97de41091930ed3d167eb11881d4097c51deac407091d39ddd1ee8bf1fde3f946844a17a7
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"unist-util-generated@npm:^2.0.0":
  version: 2.0.1
  resolution: "unist-util-generated@npm:2.0.1"
  checksum: 10c0/6f052dd47a7280785f3787f52cdfe8819e1de50317a1bcf7c9346c63268cf2cebc61a5980e7ca734a54735e27dbb73091aa0361a98504ab7f9409fb75f1b16bb
  languageName: node
  linkType: hard

"unist-util-is@npm:^5.0.0":
  version: 5.2.1
  resolution: "unist-util-is@npm:5.2.1"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10c0/a2376910b832bb10653d2167c3cd85b3610a5fd53f5169834c08b3c3a720fae9043d75ad32d727eedfc611491966c26a9501d428ec62467edc17f270feb5410b
  languageName: node
  linkType: hard

"unist-util-is@npm:^6.0.0":
  version: 6.0.0
  resolution: "unist-util-is@npm:6.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/9419352181eaa1da35eca9490634a6df70d2217815bb5938a04af3a662c12c5607a2f1014197ec9c426fbef18834f6371bfdb6f033040fa8aa3e965300d70e7e
  languageName: node
  linkType: hard

"unist-util-position-from-estree@npm:^1.0.0, unist-util-position-from-estree@npm:^1.1.0":
  version: 1.1.2
  resolution: "unist-util-position-from-estree@npm:1.1.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10c0/1d95d0b2b05efcec07a4e6745a6950cd498f6100fb900615b252937baed5140df1c6319b9a67364c8a6bd891c58b3c9a52a22e8e1d3422c50bb785d7e3ad7484
  languageName: node
  linkType: hard

"unist-util-position@npm:^4.0.0":
  version: 4.0.4
  resolution: "unist-util-position@npm:4.0.4"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10c0/e506d702e25a0fb47a64502054f709a6ff5db98993bf139eec868cd11eb7de34392b781c6c2002e2c24d97aa398c14b32a47076129f36e4b894a2c1351200888
  languageName: node
  linkType: hard

"unist-util-position@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-position@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/dde3b31e314c98f12b4dc6402f9722b2bf35e96a4f2d463233dd90d7cde2d4928074a7a11eff0a5eb1f4e200f27fc1557e0a64a7e8e4da6558542f251b1b7400
  languageName: node
  linkType: hard

"unist-util-remove-position@npm:^4.0.0":
  version: 4.0.2
  resolution: "unist-util-remove-position@npm:4.0.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-visit: "npm:^4.0.0"
  checksum: 10c0/17371b1e53c52d1b00656c9c6fe1bb044846e7067022195823ed3d1a8d8b965d4f9a79b286b8a841e68731b4ec93afd563b81ae92151f80c28534ba51e9dc18f
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^3.0.0":
  version: 3.0.3
  resolution: "unist-util-stringify-position@npm:3.0.3"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10c0/14550027825230528f6437dad7f2579a841780318569851291be6c8a970bae6f65a7feb24dabbcfce0e5e68cacae85bf12cbda3f360f7c873b4db602bdf7bb21
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^4.0.0":
  version: 4.0.0
  resolution: "unist-util-stringify-position@npm:4.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/dfe1dbe79ba31f589108cb35e523f14029b6675d741a79dea7e5f3d098785045d556d5650ec6a8338af11e9e78d2a30df12b1ee86529cded1098da3f17ee999e
  languageName: node
  linkType: hard

"unist-util-visit-children@npm:^2.0.2":
  version: 2.0.2
  resolution: "unist-util-visit-children@npm:2.0.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
  checksum: 10c0/d43d80f35b6845a37d6a52ff8b9065401e779c30ba7323e83fb54b980007483027db955ae6a34904754b8b1b5e7d764d921546251b85096203ca5116c1b05596
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^5.0.0, unist-util-visit-parents@npm:^5.1.1, unist-util-visit-parents@npm:^5.1.3":
  version: 5.1.3
  resolution: "unist-util-visit-parents@npm:5.1.3"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-is: "npm:^5.0.0"
  checksum: 10c0/f6829bfd8f2eddf63a32e2c302cd50978ef0c194b792c6fe60c2b71dfd7232415a3c5941903972543e9d34e6a8ea69dee9ccd95811f4a795495ed2ae855d28d0
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^6.0.0":
  version: 6.0.1
  resolution: "unist-util-visit-parents@npm:6.0.1"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
  checksum: 10c0/51b1a5b0aa23c97d3e03e7288f0cdf136974df2217d0999d3de573c05001ef04cccd246f51d2ebdfb9e8b0ed2704451ad90ba85ae3f3177cf9772cef67f56206
  languageName: node
  linkType: hard

"unist-util-visit@npm:^4.0.0, unist-util-visit@npm:^4.1.2":
  version: 4.1.2
  resolution: "unist-util-visit@npm:4.1.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-is: "npm:^5.0.0"
    unist-util-visit-parents: "npm:^5.1.1"
  checksum: 10c0/56a1f49a4d8e321e75b3c7821d540a45165a031dd06324bb0e8c75e7737bc8d73bdddbf0b0ca82000f9708a4c36861c6ebe88d01f7cf00e925f5d75f13a3a017
  languageName: node
  linkType: hard

"unist-util-visit@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-visit@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
    unist-util-visit-parents: "npm:^6.0.0"
  checksum: 10c0/51434a1d80252c1540cce6271a90fd1a106dbe624997c09ed8879279667fb0b2d3a685e02e92bf66598dcbe6cdffa7a5f5fb363af8fdf90dda6c855449ae39a5
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"uuid@npm:^11.1.0":
  version: 11.1.0
  resolution: "uuid@npm:11.1.0"
  bin:
    uuid: dist/esm/bin/uuid
  checksum: 10c0/34aa51b9874ae398c2b799c88a127701408cd581ee89ec3baa53509dd8728cbb25826f2a038f9465f8b7be446f0fbf11558862965b18d21c993684297628d4d3
  languageName: node
  linkType: hard

"uvu@npm:^0.5.0":
  version: 0.5.6
  resolution: "uvu@npm:0.5.6"
  dependencies:
    dequal: "npm:^2.0.0"
    diff: "npm:^5.0.0"
    kleur: "npm:^4.0.3"
    sade: "npm:^1.7.3"
  bin:
    uvu: bin.js
  checksum: 10c0/ad32eb5f7d94bdeb71f80d073003f0138e24f61ed68cecc8e15d2f30838f44c9670577bb1775c8fac894bf93d1bc1583d470a9195e49bfa6efa14cc6f4942bff
  languageName: node
  linkType: hard

"varint@npm:^6.0.0":
  version: 6.0.0
  resolution: "varint@npm:6.0.0"
  checksum: 10c0/737fc37088a62ed3bd21466e318d21ca7ac4991d0f25546f518f017703be4ed0f9df1c5559f1dd533dddba4435a1b758fd9230e4772c1a930ef72b42f5c750fd
  languageName: node
  linkType: hard

"vfile-location@npm:^4.0.0":
  version: 4.1.0
  resolution: "vfile-location@npm:4.1.0"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    vfile: "npm:^5.0.0"
  checksum: 10c0/77097e819579214d3346aaa2b06e4d23e2413221ac4914679d312cf64973011b76f0e2424fa8f18987befcd6ed60f4f6c4c6ebd5d5326062173a95f6b4445a96
  languageName: node
  linkType: hard

"vfile-location@npm:^5.0.0":
  version: 5.0.3
  resolution: "vfile-location@npm:5.0.3"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10c0/1711f67802a5bc175ea69750d59863343ed43d1b1bb25c0a9063e4c70595e673e53e2ed5cdbb6dcdc370059b31605144d95e8c061b9361bcc2b036b8f63a4966
  languageName: node
  linkType: hard

"vfile-message@npm:^3.0.0":
  version: 3.1.4
  resolution: "vfile-message@npm:3.1.4"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
  checksum: 10c0/c4ccf9c0ced92d657846fd067fefcf91c5832cdbe2ecc431bb67886e8c959bf7fc05a9dbbca5551bc34c9c87a0a73854b4249f65c64ddfebc4d59ea24a18b996
  languageName: node
  linkType: hard

"vfile-message@npm:^4.0.0":
  version: 4.0.2
  resolution: "vfile-message@npm:4.0.2"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-stringify-position: "npm:^4.0.0"
  checksum: 10c0/07671d239a075f888b78f318bc1d54de02799db4e9dce322474e67c35d75ac4a5ac0aaf37b18801d91c9f8152974ea39678aa72d7198758b07f3ba04fb7d7514
  languageName: node
  linkType: hard

"vfile@npm:^5.0.0":
  version: 5.3.7
  resolution: "vfile@npm:5.3.7"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    is-buffer: "npm:^2.0.0"
    unist-util-stringify-position: "npm:^3.0.0"
    vfile-message: "npm:^3.0.0"
  checksum: 10c0/c36bd4c3f16ec0c6cbad0711ca99200316bbf849d6b07aa4cb5d9062cc18ae89249fe62af9521926e9659c0e6bc5c2c1da0fe26b41fb71e757438297e1a41da4
  languageName: node
  linkType: hard

"vfile@npm:^6.0.0":
  version: 6.0.3
  resolution: "vfile@npm:6.0.3"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    vfile-message: "npm:^4.0.0"
  checksum: 10c0/e5d9eb4810623f23758cfc2205323e33552fb5972e5c2e6587babe08fe4d24859866277404fb9e2a20afb71013860d96ec806cb257536ae463c87d70022ab9ef
  languageName: node
  linkType: hard

"vscode-jsonrpc@npm:8.2.0":
  version: 8.2.0
  resolution: "vscode-jsonrpc@npm:8.2.0"
  checksum: 10c0/0789c227057a844f5ead55c84679206227a639b9fb76e881185053abc4e9848aa487245966cc2393fcb342c4541241b015a1a2559fddd20ac1e68945c95344e6
  languageName: node
  linkType: hard

"vscode-languageserver-protocol@npm:3.17.5":
  version: 3.17.5
  resolution: "vscode-languageserver-protocol@npm:3.17.5"
  dependencies:
    vscode-jsonrpc: "npm:8.2.0"
    vscode-languageserver-types: "npm:3.17.5"
  checksum: 10c0/5f38fd80da9868d706eaa4a025f4aff9c3faad34646bcde1426f915cbd8d7e8b6c3755ce3fef6eebd256ba3145426af1085305f8a76e34276d2e95aaf339a90b
  languageName: node
  linkType: hard

"vscode-languageserver-textdocument@npm:~1.0.11":
  version: 1.0.12
  resolution: "vscode-languageserver-textdocument@npm:1.0.12"
  checksum: 10c0/534349894b059602c4d97615a1147b6c4c031141c2093e59657f54e38570f5989c21b376836f13b9375419869242e9efb4066643208b21ab1e1dee111a0f00fb
  languageName: node
  linkType: hard

"vscode-languageserver-types@npm:3.17.5":
  version: 3.17.5
  resolution: "vscode-languageserver-types@npm:3.17.5"
  checksum: 10c0/1e1260de79a2cc8de3e46f2e0182cdc94a7eddab487db5a3bd4ee716f67728e685852707d72c059721ce500447be9a46764a04f0611e94e4321ffa088eef36f8
  languageName: node
  linkType: hard

"vscode-languageserver@npm:~9.0.1":
  version: 9.0.1
  resolution: "vscode-languageserver@npm:9.0.1"
  dependencies:
    vscode-languageserver-protocol: "npm:3.17.5"
  bin:
    installServerIntoExtension: bin/installServerIntoExtension
  checksum: 10c0/8a0838d77c98a211c76e54bd3a6249fc877e4e1a73322673fb0e921168d8e91de4f170f1d4ff7e8b6289d0698207afc6aba6662d4c1cd8e4bd7cae96afd6b0c2
  languageName: node
  linkType: hard

"vscode-uri@npm:~3.0.8":
  version: 3.0.8
  resolution: "vscode-uri@npm:3.0.8"
  checksum: 10c0/f7f217f526bf109589969fe6e66b71e70b937de1385a1d7bb577ca3ee7c5e820d3856a86e9ff2fa9b7a0bc56a3dd8c3a9a557d3fedd7df414bc618d5e6b567f9
  languageName: node
  linkType: hard

"web-namespaces@npm:^2.0.0":
  version: 2.0.1
  resolution: "web-namespaces@npm:2.0.1"
  checksum: 10c0/df245f466ad83bd5cd80bfffc1674c7f64b7b84d1de0e4d2c0934fb0782e0a599164e7197a4bce310ee3342fd61817b8047ff04f076a1ce12dd470584142a4bd
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/baad244e6e33335ea24e86e51868fe6823626e3a3c88d9a6674642afff1d34d9a154c917e74af8d845fd25d170c4ea9cf69a47133c3f3656e1252b3d462d9f6c
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"x-fetch@npm:^0.2.6":
  version: 0.2.6
  resolution: "x-fetch@npm:0.2.6"
  checksum: 10c0/f6510ae0b5d612fb0f5ce3f55552468d5979040f171a4e69bd718a36e741c4d8e6154697557be6e771fba3fe4467d730d50b3d87d8f70d15d92dedefa9a07fbe
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10c0/366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10c0/4df2842c36e468590c3691c894bc9cdbac41f520566e76e24f59401ba7d8b4811eb1e34524d57e54bc6d864bcb66baab7ffd9ca42bf1eda596618f9162b91249
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yaml@npm:^1.10.0":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: 10c0/5c28b9eb7adc46544f28d9a8d20c5b3cb1215a886609a2fd41f51628d8aaa5878ccd628b755dbcd29f6bb4921bd04ffbc6dcc370689bb96e594e2f9813d2605f
  languageName: node
  linkType: hard

"yaml@npm:^2.7.1":
  version: 2.7.1
  resolution: "yaml@npm:2.7.1"
  bin:
    yaml: bin.mjs
  checksum: 10c0/ee2126398ab7d1fdde566b4013b68e36930b9e6d8e68b6db356875c99614c10d678b6f45597a145ff6d63814961221fc305bf9242af8bf7450177f8a68537590
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.0.1, yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10c0/f84b5e48169479d2f402239c59f084cfd1c3acc197a05c59b98bab067452e6b3ea46d4dd8ba2985ba7b3d32a343d77df0debd6b343e5dae3da2aab2cdf5886b2
  languageName: node
  linkType: hard

"yargs@npm:^17.0.1, yargs@npm:^17.7.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10c0/ccd7e723e61ad5965fffbb791366db689572b80cca80e0f96aad968dfff4156cd7cd1ad18607afe1046d8241e6fb2d6c08bf7fa7bfb5eaec818735d8feac8f05
  languageName: node
  linkType: hard

"yoctocolors-cjs@npm:^2.1.2":
  version: 2.1.2
  resolution: "yoctocolors-cjs@npm:2.1.2"
  checksum: 10c0/a0e36eb88fea2c7981eab22d1ba45e15d8d268626e6c4143305e2c1628fa17ebfaa40cd306161a8ce04c0a60ee0262058eab12567493d5eb1409780853454c6f
  languageName: node
  linkType: hard

"yoctocolors@npm:^2.1.1":
  version: 2.1.1
  resolution: "yoctocolors@npm:2.1.1"
  checksum: 10c0/85903f7fa96f1c70badee94789fade709f9d83dab2ec92753d612d84fcea6d34c772337a9f8914c6bed2f5fc03a428ac5d893e76fab636da5f1236ab725486d0
  languageName: node
  linkType: hard

"zwitch@npm:^2.0.0, zwitch@npm:^2.0.4":
  version: 2.0.4
  resolution: "zwitch@npm:2.0.4"
  checksum: 10c0/3c7830cdd3378667e058ffdb4cf2bb78ac5711214e2725900873accb23f3dfe5f9e7e5a06dcdc5f29605da976fc45c26d9a13ca334d6eea2245a15e77b8fc06e
  languageName: node
  linkType: hard
